import type { User } from "database/schema";
import { createCookieSessionStorage, redirect } from "react-router";

// 创建一个函数来获取 sessionStorage，这样可以访问运行时的环境变量
function getSessionStorage(authSecret: string) {
  return createCookieSessionStorage({
    cookie: {
      name: "auth-token",
      httpOnly: true,
      path: "/",
      sameSite: "lax",
      secure: process.env.NODE_ENV === "production",
      secrets: [authSecret],
    },
  });
}

export async function createUserSession({
  request,
  user,
  remember,
  redirectTo,
  authSecret,
}: {
  request: Request;
  user: User;
  remember: boolean;
  redirectTo: string;
  authSecret: string;
}) {
  const sessionStorage = getSessionStorage(authSecret);
  const session = await sessionStorage.getSession(request.headers.get("Cookie"));
  session.set("user", user);

  return redirect(redirectTo, {
    headers: {
      "Set-Cookie": await sessionStorage.commitSession(session, {
        maxAge: remember ? 60 * 60 * 24 * 7 : undefined,
      }),
    },
  });
}

export async function destroyUserSession(request: Request, authSecret: string) {
  const sessionStorage = getSessionStorage(authSecret);
  const session = await sessionStorage.getSession(request.headers.get("Cookie"));

  return redirect("/", {
    headers: {
      "Set-Cookie": await sessionStorage.destroySession(session),
    },
  });
}

export async function requireUserSession(request: Request, authSecret: string): Promise<User | undefined> {
  const sessionStorage = getSessionStorage(authSecret);
  const session = await sessionStorage.getSession(request.headers.get("Cookie"));
  const user = session.get("user") as User | undefined;

  return user;
}

export async function requireAdminSession(request: Request, authSecret: string) {
  const user = await requireUserSession(request, authSecret);

  if (user?.role !== "admin") {
    throw redirect("/");
  }

  return user;
}

export async function requireMemberSession(request: Request, authSecret: string) {
  const user = await requireUserSession(request, authSecret);

  if (!user?.isMember) {
    throw redirect("/");
  }

  return user;
}