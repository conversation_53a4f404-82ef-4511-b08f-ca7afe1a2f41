import { Figma, Loader2 } from "lucide-react"
import { cn } from "~/lib/utils"
import { Button } from "~/components/ui/button"
import { useCallback, useState } from "react"

export type LoginFormProps = {
  className?: string
  figmaClientId: string
  figmaRedirectUri: string
  figmaClientSecret: string
  clientState?: string
}

export function LoginForm({
  className,
  figmaClientId,
  figmaRedirectUri,
  figmaClientSecret,
  clientState,
}: LoginFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const handleFigmaLogin = useCallback(() => {
    setIsLoading(true)
    const clientId = figmaClientId;
    const redirectUri = encodeURIComponent(figmaRedirectUri);
    const scope = encodeURIComponent("files:read");
    const state = clientState || Math.random().toString(36).substring(2, 15); // 生成随机 state 用于安全验证
    const authUrl = `https://www.figma.com/oauth?client_id=${clientId}&redirect_uri=${redirectUri}&scope=${scope}&state=${state}&response_type=code`;
    window.location.href = authUrl;
  }, [figmaClientId, figmaRedirectUri, clientState])

  return (
    <div className={cn("flex flex-col gap-6", className)}>
      <div className="flex flex-col gap-4">
        <div className="flex flex-col items-center gap-2">
          <a
            href="/"
            className="flex flex-col items-center gap-2 font-medium"
          >
            <div className="flex h-12 w-12 items-center justify-center rounded-md">
              <img src="/android-chrome-512x512.png" alt="PlugMate" className="size-12" />
            </div>
            <span className="sr-only">PlugMate.</span>
          </a>
          <h1 className="text-xl font-bold">PlugMate</h1>
          <h2 className="font-light text-sm">不断扩展的设计资产和插件库</h2>
          {/* <div className="text-center text-sm">
              Don&apos;t have an account?{" "}
              <a href="#" className="underline underline-offset-4">
                Sign up
              </a>
            </div> */}
        </div>
        {/* <div className="flex flex-col gap-6">
            <div className="grid gap-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                required
              />
            </div>
            <Button type="submit" className="w-full">
              Login
            </Button>
          </div>
          <div className="relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t after:border-border">
            <span className="relative z-10 bg-background px-2 text-muted-foreground">
              Or
            </span>
          </div> */}
        <div className="grid gap-4 sm:grid-cols-1">
          <Button variant="outline" className="w-full cursor-pointer" onClick={handleFigmaLogin} disabled={isLoading}>
            {isLoading ? <Loader2 className="size-4 animate-spin" /> : <Figma className="size-4" />}
            使用 Figma 账号一键登录
          </Button>
        </div>
      </div>
      {/* <div className="text-balance text-center text-xs text-muted-foreground [&_a]:underline [&_a]:underline-offset-4 hover:[&_a]:text-primary  ">
        By clicking continue, you agree to our <a href="#">Terms of Service</a>{" "}
        and <a href="#">Privacy Policy</a>.
      </div> */}
    </div>
  )
}
