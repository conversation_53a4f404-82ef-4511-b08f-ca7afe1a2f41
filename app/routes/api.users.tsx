import { data } from "react-router";
import type { Route } from "./+types/api.users";
import { requireAdminSession } from "~/session.server";
import { 
  getUsersList, 
  updateUserMembership, 
  updateUserRole,
  findUserByEmail,
  addReservedMember,
  checkReservedMemberExists,
  type GetUsersParams
} from "~/lib/users.server";

export async function loader({ request, context }: Route.LoaderArgs) {
  await requireAdminSession(request, context.cloudflare.env.AUTH_SECRET);

  const url = new URL(request.url);
  const page = Number(url.searchParams.get("page")) || 1;
  const pageSize = Number(url.searchParams.get("pageSize")) || 10;
  const search = url.searchParams.get("search") || "";
  const orderBy = (url.searchParams.get("orderBy") as GetUsersParams['orderBy']) || "createdAt";
  const orderDirection = (url.searchParams.get("orderDirection") as GetUsersParams['orderDirection']) || "desc";

  try {
    const result = await getUsersList(context.db, {
      page,
      pageSize,
      search,
      orderBy,
      orderDirection
    });
    
    return data({ 
      ...result,
      search,
      orderBy,
      orderDirection
    });
  } catch (error) {
    console.error("获取用户列表失败：", error);
    return data({ error: "获取用户列表失败" }, { status: 500 });
  }
}

export async function action({ request, context }: Route.ActionArgs) {
  await requireAdminSession(request, context.cloudflare.env.AUTH_SECRET);

  const formData = await request.formData();
  const actionType = formData.get("_action") as string;

  try {
    switch (actionType) {
      case "updateMembership": {
        const userId = Number(formData.get("userId"));
        const isMember = formData.get("isMember") === "true";

        if (!userId) {
          return data({ error: "用户 ID 不能为空" }, { status: 400 });
        }

        await updateUserMembership(context.db, userId, isMember);
        return data({ success: true, message: `用户会员状态${isMember ? "开启" : "关闭"}成功` });
      }

      case "updateRole": {
        const userId = Number(formData.get("userId"));
        const role = formData.get("role") as string;

        if (!userId || !role) {
          return data({ error: "用户 ID 和角色不能为空" }, { status: 400 });
        }

        if (!["admin", "user"].includes(role)) {
          return data({ error: "无效的角色类型" }, { status: 400 });
        }

        await updateUserRole(context.db, userId, role);
        return data({ success: true, message: `用户角色更新为${role === "admin" ? "管理员" : "普通用户"}成功` });
      }

      case "addReservedMember": {
        const email = formData.get("email") as string;

        if (!email) {
          return data({ error: "邮箱地址不能为空" }, { status: 400 });
        }

        // 验证邮箱格式
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
          return data({ error: "邮箱格式不正确" }, { status: 400 });
        }

        // 检查用户是否已存在
        const existingUser = await findUserByEmail(context.db, email);
        
        if (existingUser) {
          // 用户存在，直接更新会员状态
          if (existingUser.isMember) {
            return data({ error: "该用户已经是会员了" }, { status: 400 });
          }
          
          await updateUserMembership(context.db, existingUser.id, true);
          return data({ 
            success: true, 
            message: "用户已存在，会员状态已更新", 
            type: "existing_user" 
          });
        } else {
          // 用户不存在，检查是否已经在预定会员列表中
          const existingReservedMember = await checkReservedMemberExists(context.db, email);
          
          if (existingReservedMember) {
            return data({ error: "该邮箱已在预定会员列表中" }, { status: 400 });
          }

          // 添加到预定会员列表
          await addReservedMember(context.db, email);
          return data({ 
            success: true, 
            message: "预定会员添加成功，用户注册后将自动获得会员资格",
            type: "reserved_member"
          });
        }
      }

      default:
        return data({ error: "无效的操作" }, { status: 400 });
    }
  } catch (error) {
    console.error("用户操作失败：", error);
    return data({ error: "操作失败，请重试" }, { status: 500 });
  }
} 