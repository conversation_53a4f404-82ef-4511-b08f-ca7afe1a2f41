import type { Route } from "./+types/api.login";

export async function action({ request, context }: Route.ActionArgs) {
  // const formData = await request.formData();
  // const email = formData.get("email") as string;
  // const validationCode = Math.floor(100000 + Math.random() * 900000).toString();
  // console.info(context.cloudflare.env);
  // const PLUNK_API_KEY = context.cloudflare.env.PLUNK_API_KEY;
  // const plunk = new Plunk(PLUNK_API_KEY);

  // const emailHtml = await render(<SigninEmail validationCode={validationCode} />);

  // const resp = await plunk.sendEmail({
  //   to: email,
  //   subject: "Login to ZhangHe Dev",
  //   body: emailHtml
  // });

  // console.log(resp);

  return {
    success: true,
    message: "Email sent successfully",
  }
}