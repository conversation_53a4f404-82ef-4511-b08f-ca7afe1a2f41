import type { ActionFunctionArgs } from "react-router";
import { parseFormData } from "@mjackson/form-data-parser";
import {
  createPlugin,
  updatePlugin,
  deletePlugin,
  getPluginById,
} from "~/lib/plugins.server";
import { requireAdminSession } from "~/session.server";

// Placeholder for R2 upload logic
async function uploadFileToR2(file: File, env: any, existingKey?: string) {
  // If an existing key is provided, delete the old object first
  if (existingKey) {
    try {
      await env.R2_BUCKET.delete(existingKey);
    } catch (e) {
      console.warn(`Failed to delete old R2 object: ${existingKey}`, e);
    }
  }
  const objectKey = `plugins-${Date.now()}-${file.name}`;
  await env.R2_BUCKET.put(objectKey, file.stream());

  // Construct the public URL for the uploaded file.
  // Ensure R2_PUBLIC_URL is configured in your environment (e.g., via wrangler.toml or Cloudflare dashboard).
  // It should be the base public URL for your R2 bucket, e.g., https://your-r2-public-domain.com
  if (!env.R2_PUBLIC_URL) {
    console.error(
      "R2_PUBLIC_URL is not configured in the environment. Returning object key instead of public URL. " +
      "Please set R2_PUBLIC_URL to the public URL of your R2 bucket."
    );
    // Fallback to returning the object key if the public URL base is not configured.
    // In a production environment, you might want to throw an error here.
    return objectKey;
  }

  // Ensure the base URL doesn't have a trailing slash before appending the object key.
  const baseUrl = env.R2_PUBLIC_URL.endsWith('/') ? env.R2_PUBLIC_URL.slice(0, -1) : env.R2_PUBLIC_URL;
  const publicUrl = `${baseUrl}/${objectKey}`;

  return publicUrl;
}

export async function action({ request, context }: ActionFunctionArgs) {
  await requireAdminSession(request, context.cloudflare.env.AUTH_SECRET);
  const { db, cloudflare } = context;
  const env = cloudflare.env;

  const formData = await request.formData();
  const actionType = formData.get("_action") as string;

  try {
    if (actionType === "create" || actionType === "update") {
      const name = formData.get("name") as string;
      const categoryId = parseInt(formData.get("categoryId") as string);
      const url = formData.get("url") as string;
      const description = formData.get("description") as string;
      const isRecommended = formData.get("isRecommended") === "on" ? 1 : 0;
      const recommendScore = parseInt(formData.get("recommendScore") as string || "0");
      const isPaid = formData.get("isPaid") === "on" ? 1 : 0;
      const author = formData.get("author") as string || "";
      const recommendStartTime = formData.get("recommendStartTime") ? Date.parse(formData.get("recommendStartTime") as string) : null;
      const recommendEndTime = formData.get("recommendEndTime") ? Date.parse(formData.get("recommendEndTime") as string) : null;
      
      let logoKey = formData.get("existingLogoKey") as string || "";
      const logoFile = formData.get("logo") as File;
      if (logoFile && logoFile.size > 0) {
        logoKey = await uploadFileToR2(logoFile, env, logoKey || undefined);
      }

      let coversKeysString = formData.get("existingCoversKeys") as string || "[]";
      let coversKeys: string[] = JSON.parse(coversKeysString);
      const newCoversFiles = formData.getAll("covers") as File[];
      
      // Handle deletion of specific existing covers if an update to covers is made
      const deletedCoverIndicesString = formData.get("deletedCoverIndices") as string;
      if (deletedCoverIndicesString) {
        const deletedIndices: number[] = JSON.parse(deletedCoverIndicesString);
        const newCoversKeys: string[] = [];
        for (let i = 0; i < coversKeys.length; i++) {
          if (!deletedIndices.includes(i)) {
            newCoversKeys.push(coversKeys[i]);
          } else {
            // Optionally delete from R2 if needed, for now just remove from DB list
            try {
                await env.R2_BUCKET.delete(coversKeys[i]);
            } catch (e) {
                console.warn(`Failed to delete old R2 cover object: ${coversKeys[i]}`, e);
            }
          }
        }
        coversKeys = newCoversKeys;
      }

      if (newCoversFiles && newCoversFiles.length > 0) {
        for (const file of newCoversFiles) {
          if (file && file.size > 0) {
            const coverKey = await uploadFileToR2(file, env);
            coversKeys.push(coverKey);
          }
        }
      }

      const pluginData = {
        name,
        categoryId,
        url,
        description,
        logo: logoKey,
        covers: JSON.stringify(coversKeys),
        isRecommended,
        recommendScore,
        recommendStartTime: recommendStartTime ?? undefined,
        recommendEndTime: recommendEndTime ?? undefined,
        isPaid,
        author,
      };

      if (actionType === "create") {
        await createPlugin(db, pluginData);
        return new Response(JSON.stringify({ success: true, message: "插件创建成功" }), { status: 200, headers: { 'Content-Type': 'application/json' } });
      }

      if (actionType === "update") {
        const id = parseInt(formData.get("id") as string);
        await updatePlugin(db, id, pluginData);
        return new Response(JSON.stringify({ success: true, message: "插件更新成功" }), { status: 200, headers: { 'Content-Type': 'application/json' } });
      }
    }

    if (actionType === "delete") {
      const id = parseInt(formData.get("id") as string);
      // Optionally, retrieve plugin details to delete associated R2 objects before deleting from DB
      const pluginToDelete = await getPluginById(db, id); // You might need to implement getPluginById
      if (pluginToDelete) {
        if (pluginToDelete.logo) {
            try {
                await env.R2_BUCKET.delete(pluginToDelete.logo);
            } catch (e) {
                console.warn(`Failed to delete R2 logo object: ${pluginToDelete.logo}`, e);
            }
        }
        if (pluginToDelete.covers) {
            const coverKeysToDelete: string[] = JSON.parse(pluginToDelete.covers);
            for (const key of coverKeysToDelete) {
                try {
                    await env.R2_BUCKET.delete(key);
                } catch (e) {
                     console.warn(`Failed to delete R2 cover object: ${key}`, e);
                }
            }
        }
      }
      await deletePlugin(db, id);
      return new Response(JSON.stringify({ success: true, message: "插件删除成功" }), { status: 200, headers: { 'Content-Type': 'application/json' } });
    }

    return new Response(JSON.stringify({ success: false, error: "无效的操作" }), { status: 400, headers: { 'Content-Type': 'application/json' } });
  } catch (error: any) {
    console.error("插件操作失败：", error);
    return new Response(JSON.stringify({ success: false, error: error.message }), { status: 500, headers: { 'Content-Type': 'application/json' } });
  }
} 