import { reservedMembers, users } from "database/schema";
import type { Route } from "./+types/auth.callback";
import ky from "ky";
import { createUserSession } from "~/session.server";
import { data } from "react-router";
import { eq } from "drizzle-orm";

// ?code=ve28gSa13nSNm2WQH4Qx3XrZ0&state=o67d2rdm2vf
export async function loader({ request, context }: Route.LoaderArgs) {
  const url = new URL(request.url);
  const code = url.searchParams.get("code");
  const state = url.searchParams.get("state");

  if (!code || !state) {
    return { error: "缺少必备参数" }
  }

  try {
    // 创建 Base64 编码的 client_id:client_secret
    const credentials = btoa(`${context.cloudflare.env.FIGMA_CLIENT_ID}:${context.cloudflare.env.FIGMA_CLIENT_SECRET}`);

    // 创建 form data
    const formData = new URLSearchParams({
      redirect_uri: context.cloudflare.env.FIGMA_REDIRECT_URI,
      code,
      grant_type: "authorization_code",
    });

    const oauth = await ky.post("https://api.figma.com/v1/oauth/token", {
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
        Authorization: `Basic ${credentials}`,
      },
      body: formData,
    }).json<{
      user_id_string: string
      access_token: string
      token_type: string
      expires_in: number
      refresh_token: string
    }>()

    // 获取用户信息
    const userInfo = await ky.get("https://api.figma.com/v1/me", {
      headers: {
        "Authorization": `Bearer ${oauth.access_token}`,
      },
    }).json<{
      id: string
      email: string
      handle: string
      img_url: string
    }>()

    // 根据取到的用户信息，查询数据库中是否存在 figmaId 为该用户，如果存在就直接登录，如果不存在，则创建用户
    let user = await context.db.query.users.findFirst({
      where: (users, { eq }) => eq(users.figmaId, userInfo.id),
    })

    if (!user) {
      const ADMIN_EMAILS = ["<EMAIL>", "<EMAIL>"]
      const isAdmin = ADMIN_EMAILS.includes(userInfo.email)
      let isReservedMember = false

      if (!isAdmin) {
        const reservedMember = await context.db.query.reservedMembers.findFirst({
          where: (reservedMembers, { eq }) => eq(reservedMembers.email, userInfo.email),
        })
        isReservedMember = !!reservedMember
      }
      const [newUser] = await context.db.insert(users).values({
        figmaId: userInfo.id,
        email: userInfo.email,
        name: userInfo.handle,
        figmaAccessToken: oauth.access_token,
        figmaRefreshToken: oauth.refresh_token,
        figmaImageUrl: userInfo.img_url,
        tokenExpiresAt: oauth.expires_in,
        role: isAdmin ? "admin" : "user",
        isMember: isAdmin || isReservedMember ? 1 : 0,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      }).returning()
      user = newUser

      // 是预留会员，则删除预留会员
      if (!isAdmin && isReservedMember) {
        await context.db.delete(reservedMembers).where(eq(reservedMembers.email, userInfo.email))
      }
    }

    if (user) {
      return createUserSession({
        request,
        user,
        remember: true,
        redirectTo: user.role === "admin" ? "/admin" : "/",
        authSecret: context.cloudflare.env.AUTH_SECRET,
      });
    }
    
    return data({ error: "用户不存在" }, { status: 404 });
  } catch (error) {
    console.error("OAuth error:", error)
    // return { error: "授权失败，请重试" }
    throw data("授权失败，请重试", { status: 404 });
  }
}