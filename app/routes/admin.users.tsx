import type { Route } from "./+types/admin.users";
import { use<PERSON><PERSON><PERSON>, use<PERSON>oaderData } from "react-router";
import { useState, useEffect } from "react";
import { But<PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "~/components/ui/dialog";
import { Badge } from "~/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "~/components/ui/pagination";
import { 
  Search, 
  Edit, 
  CheckCircle, 
  Users, 
  Crown, 
  UserPlus,
  ChevronUp,
  ChevronDown
} from "lucide-react";
import { toast } from "sonner";
import type { User } from "database/schema";
import { requireAdminSession } from "~/session.server";
import { getUsersList } from "~/lib/users.server";
import { Switch } from "~/components/ui/switch";

export function meta({ }: Route.MetaArgs) {
  return [
    { title: "用户管理 | Plugmate" },
    { name: "description", content: "用户管理" },
  ];
}

export async function loader({ request, context }: Route.LoaderArgs) {
  await requireAdminSession(request, context.cloudflare.env.AUTH_SECRET);
  
  const url = new URL(request.url);
  const page = Number(url.searchParams.get("page")) || 1;
  const pageSize = Number(url.searchParams.get("pageSize")) || 10;
  const search = url.searchParams.get("search") || "";
  const orderBy = url.searchParams.get("orderBy") || "createdAt";
  const orderDirection = url.searchParams.get("orderDirection") || "desc";
  
  const result = await getUsersList(context.db, {
    page,
    pageSize,
    search,
    orderBy: orderBy as any,
    orderDirection: orderDirection as any
  });
  
  return { 
    ...result,
    search,
    orderBy,
    orderDirection
  };
}

export default function AdminUsers() {
  const data = useLoaderData<typeof loader>();
  const fetcher = useFetcher();
  const [search, setSearch] = useState(data.search);
  const [addMemberDialogOpen, setAddMemberDialogOpen] = useState(false);
  const [editUserDialogOpen, setEditUserDialogOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [newMemberEmail, setNewMemberEmail] = useState("");

  // 监听表单提交状态
  useEffect(() => {
    if (fetcher.data) {
      if (fetcher.data.success) {
        toast.success(fetcher.data.message || "操作成功");
        setAddMemberDialogOpen(false);
        setEditUserDialogOpen(false);
        setEditingUser(null);
        setNewMemberEmail("");
      } else if (fetcher.data.error) {
        toast.error(fetcher.data.error);
      }
    }
  }, [fetcher.data]);

  // 搜索处理
  const handleSearch = () => {
    const url = new URL(window.location.href);
    url.searchParams.set("page", "1"); // 重置到第一页
    if (search) {
      url.searchParams.set("search", search);
    } else {
      url.searchParams.delete("search");
    }
    window.location.href = url.toString();
  };

  // 排序处理
  const handleSort = (column: string) => {
    const url = new URL(window.location.href);
    const currentOrderBy = url.searchParams.get("orderBy");
    const currentDirection = url.searchParams.get("orderDirection") || "desc";
    
    if (currentOrderBy === column) {
      // 切换排序方向
      url.searchParams.set("orderDirection", currentDirection === "asc" ? "desc" : "asc");
    } else {
      // 新列，默认降序
      url.searchParams.set("orderBy", column);
      url.searchParams.set("orderDirection", "desc");
    }
    url.searchParams.set("page", "1");
    window.location.href = url.toString();
  };

  // 编辑用户
  const handleEditUser = (user: User) => {
    setEditingUser(user);
    setEditUserDialogOpen(true);
  };

  // 切换会员状态
  const handleToggleMembership = (userId: number, currentIsMember: number) => {
    fetcher.submit(
      { 
        _action: "updateMembership", 
        userId: userId.toString(),
        isMember: (!currentIsMember).toString()
      },
      { method: "post", action: "/api/users" }
    );
  };

  // 添加预定会员
  const handleAddReservedMember = () => {
    if (!newMemberEmail.trim()) {
      toast.error("请输入邮箱地址");
      return;
    }

    fetcher.submit(
      { 
        _action: "addReservedMember", 
        email: newMemberEmail.trim()
      },
      { method: "post", action: "/api/users" }
    );
  };

  const isLoading = fetcher.state !== "idle";
  const { users, pagination } = data;
  
  // 计算统计数据
  const totalUsers = pagination.total;
  const memberUsers = users.filter((user: User) => user.isMember);
  const adminUsers = users.filter((user: User) => user.role === "admin");

  // 生成分页 URL
  const buildPageUrl = (page: number) => {
    const url = new URL(window.location.href);
    url.searchParams.set("page", page.toString());
    return url.toString();
  };

  // 格式化日期
  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // 排序图标
  const SortIcon = ({ column }: { column: string }) => {
    if (data.orderBy !== column) return null;
    return data.orderDirection === "asc" ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />;
  };

  return (
    <div className="space-y-6">
      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总用户数</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{totalUsers}</div>
            <p className="text-xs text-muted-foreground">当前注册用户总数</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">会员用户</CardTitle>
            <Crown className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{memberUsers.length}</div>
            <p className="text-xs text-muted-foreground">拥有会员资格的用户数</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">管理员</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{adminUsers.length}</div>
            <p className="text-xs text-muted-foreground">管理员用户数量</p>
          </CardContent>
        </Card>
      </div>

      {/* 操作栏 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2 flex-1 max-w-md">
          <div className="relative flex-1">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索用户名或邮箱..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              onKeyDown={(e) => e.key === "Enter" && handleSearch()}
              className="pl-8"
            />
          </div>
          <Button onClick={handleSearch} variant="secondary">
            搜索
          </Button>
        </div>

        {/* 新增预定会员对话框 */}
        <Dialog open={addMemberDialogOpen} onOpenChange={setAddMemberDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <UserPlus className="size-4" />
              新增预定会员
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>新增预定会员</DialogTitle>
              <DialogDescription className="text-sm">
                输入用户邮箱，如果用户已存在将直接开通会员，如果不存在将添加到预定会员列表，待用户登录后直接就是会员身份。
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">邮箱地址 *</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="请输入用户邮箱"
                  value={newMemberEmail}
                  onChange={(e) => setNewMemberEmail(e.target.value)}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setAddMemberDialogOpen(false)}>
                取消
              </Button>
              <Button onClick={handleAddReservedMember} disabled={isLoading}>
                {isLoading ? "添加中..." : "添加"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>

      {/* 用户列表 */}
      <Card>
        <CardHeader>
          <CardTitle>用户列表</CardTitle>
          <CardDescription>
            共 {pagination.total} 个用户，当前第 {pagination.page} 页
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>用户</TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => handleSort("name")}
                >
                  <div className="flex items-center gap-1">
                    姓名
                    <SortIcon column="name" />
                  </div>
                </TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => handleSort("email")}
                >
                  <div className="flex items-center gap-1">
                    邮箱
                    <SortIcon column="email" />
                  </div>
                </TableHead>
                <TableHead>角色</TableHead>
                <TableHead>会员状态</TableHead>
                <TableHead>搜索次数</TableHead>
                <TableHead 
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => handleSort("createdAt")}
                >
                  <div className="flex items-center gap-1">
                    注册时间
                    <SortIcon column="createdAt" />
                  </div>
                </TableHead>
                <TableHead>操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {users.map((user: User) => (
                <TableRow key={user.id}>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <Avatar className="h-8 w-8">
                        <AvatarImage src={user.figmaImageUrl || undefined} />
                        <AvatarFallback>
                          {user.name.charAt(0).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                    </div>
                  </TableCell>
                  <TableCell className="font-medium">{user.name}</TableCell>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>{user.role === "admin" ? "管理员" : "普通用户"}</TableCell>
                  <TableCell>
                    <Badge variant={user.isMember ? "default" : "secondary"}>
                      {user.isMember ? "会员" : "普通"}
                    </Badge>
                  </TableCell>
                  <TableCell>{user.searchCount}</TableCell>
                  <TableCell>{formatDate(user.createdAt)}</TableCell>
                  <TableCell>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleEditUser(user)}
                      className="cursor-pointer"
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {/* 分页 */}
          {pagination.totalPages > 1 && (
            <div className="mt-4">
              <Pagination>
                <PaginationContent>
                  {pagination.page > 1 && (
                    <PaginationItem>
                      <PaginationPrevious href={buildPageUrl(pagination.page - 1)} />
                    </PaginationItem>
                  )}
                  
                  {/* 页码 */}
                  {Array.from({ length: Math.min(5, pagination.totalPages) }, (_, i) => {
                    let pageNum;
                    if (pagination.totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (pagination.page <= 3) {
                      pageNum = i + 1;
                    } else if (pagination.page >= pagination.totalPages - 2) {
                      pageNum = pagination.totalPages - 4 + i;
                    } else {
                      pageNum = pagination.page - 2 + i;
                    }
                    
                    return (
                      <PaginationItem key={pageNum}>
                        <PaginationLink 
                          href={buildPageUrl(pageNum)}
                          isActive={pageNum === pagination.page}
                        >
                          {pageNum}
                        </PaginationLink>
                      </PaginationItem>
                    );
                  })}

                  {pagination.page < pagination.totalPages && (
                    <PaginationItem>
                      <PaginationNext href={buildPageUrl(pagination.page + 1)} />
                    </PaginationItem>
                  )}
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 编辑用户对话框 */}
      <Dialog open={editUserDialogOpen} onOpenChange={setEditUserDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>用户管理</DialogTitle>
            <DialogDescription>
              查看和管理用户信息
            </DialogDescription>
          </DialogHeader>
          {editingUser && (
            <div className="space-y-6">
              {/* 用户基本信息 */}
              <div className="flex items-center gap-4 p-4 border rounded-lg bg-muted/30">
                <Avatar className="h-16 w-16">
                  <AvatarImage src={editingUser.figmaImageUrl || undefined} />
                  <AvatarFallback className="text-lg">
                    {editingUser.name.charAt(0).toUpperCase()}
                  </AvatarFallback>
                </Avatar>
                <div className="flex-1">
                  <h3 className="text-lg font-semibold">{editingUser.name}</h3>
                  <p className="text-muted-foreground">{editingUser.email}</p>
                  <div className="flex items-center gap-2 mt-2">
                    <Badge variant={editingUser.role === "admin" ? "default" : "secondary"}>
                      {editingUser.role === "admin" ? "管理员" : "普通用户"}
                    </Badge>
                    <Badge variant={editingUser.isMember ? "default" : "outline"}>
                      {editingUser.isMember ? "会员" : "普通"}
                    </Badge>
                  </div>
                </div>
              </div>
              
              {/* 详细信息网格 */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-1">
                  <Label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">用户 ID</Label>
                  <p className="text-sm font-mono bg-muted px-2 py-1 rounded">{editingUser.id}</p>
                </div>
                <div className="space-y-1">
                  <Label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Figma ID</Label>
                  <p className="text-sm font-mono bg-muted px-2 py-1 rounded break-all">{editingUser.figmaId}</p>
                </div>
                <div className="space-y-1">
                  <Label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">搜索次数</Label>
                  <p className="text-sm">{editingUser.searchCount} 次</p>
                </div>
                <div className="space-y-1">
                  <Label className="text-xs font-medium text-muted-foreground uppercase tracking-wide">注册时间</Label>
                  <p className="text-sm">{formatDate(editingUser.createdAt)}</p>
                </div>
              </div>

              {/* 会员状态管理 */}
              <div className="border-t pt-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="space-y-1">
                    <Label className="text-sm font-medium">会员状态</Label>
                    <p className="text-xs text-muted-foreground">
                      {editingUser.role === "admin" 
                        ? "管理员用户无法修改会员状态" 
                        : editingUser.isMember 
                          ? "当前用户拥有会员权限" 
                          : "当前用户为普通用户"
                      }
                    </p>
                  </div>
                  <Switch
                    checked={!!editingUser.isMember}
                    onCheckedChange={() => {
                      handleToggleMembership(editingUser.id, editingUser.isMember);
                      // 立即更新本地状态以提供即时反馈
                      setEditingUser({
                        ...editingUser,
                        isMember: editingUser.isMember ? 0 : 1
                      });
                    }}
                    disabled={isLoading || editingUser.role === "admin"}
                  />
                </div>
                {editingUser.role === "admin" && (
                  <div className="mt-2 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                    <div className="flex items-center gap-2">
                      <Crown className="h-4 w-4 text-amber-600" />
                      <p className="text-xs text-amber-800">
                        管理员用户的会员状态受到保护，无法进行修改
                      </p>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}
          <DialogFooter>
            <Button variant="outline" onClick={() => setEditUserDialogOpen(false)}>
              关闭
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}