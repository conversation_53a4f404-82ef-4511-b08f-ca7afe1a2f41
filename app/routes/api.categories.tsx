import { data } from "react-router";
import type { Route } from "./+types/api.categories";
import { categories } from "database/schema";
import { eq } from "drizzle-orm";
import { requireAdminSession } from "~/session.server";
import { getCategoriesList } from "~/lib/categories.server";

export async function loader({ request, context }: Route.LoaderArgs) {
  await requireAdminSession(request, context.cloudflare.env.AUTH_SECRET);

  const url = new URL(request.url);
  const search = url.searchParams.get("search") || "";

  try {
    const categoriesData = await getCategoriesList(context.db, search);
    return data({ categories: categoriesData, search });
  } catch (error) {
    console.error("获取分类列表失败：", error);
    return data({ error: "获取分类列表失败" }, { status: 500 });
  }
}

export async function action({ request, context }: Route.ActionArgs) {
  await requireAdminSession(request, context.cloudflare.env.AUTH_SECRET);

  const formData = await request.formData();
  const actionType = formData.get("_action") as string;

  try {
    switch (actionType) {
      case "create": {
        const name = formData.get("name") as string;
        const description = formData.get("description") as string;

        if (!name) {
          return data({ error: "分类名称不能为空" }, { status: 400 });
        }

        // 检查分类名称是否已存在
        const existingCategory = await context.db
          .select()
          .from(categories)
          .where(eq(categories.name, name))
          .limit(1);

        if (existingCategory.length > 0) {
          return data({ error: "分类名称已存在" }, { status: 400 });
        }

        const [newCategory] = await context.db
          .insert(categories)
          .values({
            name,
            description: description || null,
            createdAt: Date.now(),
            updatedAt: Date.now(),
          })
          .returning();

        return data({ success: true, category: newCategory });
      }

      case "update": {
        const id = Number(formData.get("id"));
        const name = formData.get("name") as string;
        const description = formData.get("description") as string;

        if (!id || !name) {
          return data({ error: "分类 ID 和名称不能为空" }, { status: 400 });
        }

        // 检查分类名称是否已存在（排除当前分类）
        const existingCategory = await context.db
          .select()
          .from(categories)
          .where(eq(categories.name, name))
          .limit(1);

        if (existingCategory.length > 0 && existingCategory[0].id !== id) {
          return data({ error: "分类名称已存在" }, { status: 400 });
        }

        const [updatedCategory] = await context.db
          .update(categories)
          .set({
            name,
            description: description || null,
            updatedAt: Date.now(),
          })
          .where(eq(categories.id, id))
          .returning();

        return data({ success: true, category: updatedCategory });
      }

      case "delete": {
        const id = Number(formData.get("id"));

        if (!id) {
          return data({ error: "分类 ID 不能为空" }, { status: 400 });
        }

        // 检查是否有插件使用此分类
        const pluginsUsingCategory = await context.db.query.plugins.findMany({
          where: (plugins, { eq }) => eq(plugins.categoryId, id),
        });

        if (pluginsUsingCategory.length > 0) {
          return data({
            error: `无法删除分类，还有 ${pluginsUsingCategory.length} 个插件正在使用此分类`
          }, { status: 400 });
        }

        await context.db
          .delete(categories)
          .where(eq(categories.id, id));

        return data({ success: true });
      }

      case "enable": {
        const id = Number(formData.get("id"));
        await context.db
          .update(categories)
          .set({ disabled: 0 })
          .where(eq(categories.id, id));
        return data({ success: true });
      }

      case "disable": {
        const id = Number(formData.get("id"));
        await context.db
          .update(categories)
          .set({ disabled: 1 })
          .where(eq(categories.id, id));
        return data({ success: true });
      }

      default:
        return data({ error: "无效的操作" }, { status: 400 });
    }
  } catch (error) {
    console.error("分类操作失败：", error);
    return data({ error: "操作失败，请重试" }, { status: 500 });
  }
} 