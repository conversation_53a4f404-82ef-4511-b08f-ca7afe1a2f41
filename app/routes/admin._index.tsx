import type { Route } from "./+types/admin._index";
import { useLoaderData } from "react-router";
import { requireAdminSession } from "~/session.server";
import { getDashboardStats, fillMissingDates, type DashboardStats } from "~/lib/dashboard.server";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area
} from "recharts";
import { 
  Users, 
  Shield, 
  TrendingUp, 
  Activity, 
  Star, 
  Search, 
  Crown, 
  Layers,
  DollarSign,
  UserPlus,
  Download
} from "lucide-react";
import { Badge } from "~/components/ui/badge";
import { Progress } from "~/components/ui/progress";

export function meta({ }: Route.MetaArgs) {
  return [
    { title: "数据看板 | Plugmate" },
    { name: "description", content: "数据看板" },
  ];
}

export async function loader({ request, context }: Route.LoaderArgs) {
  await requireAdminSession(request, context.cloudflare.env.AUTH_SECRET);
  
  const stats = await getDashboardStats(context.db);
  
  // 确保图表数据的连续性
  const processedStats = {
    ...stats,
    usersGrowth: fillMissingDates(stats.usersGrowth),
    pluginsGrowth: fillMissingDates(stats.pluginsGrowth),
    recommendationTrends: fillMissingDates(stats.recommendationTrends)
  };
  
  return { stats: processedStats };
}

export default function AdminIndex() {
  const { stats } = useLoaderData<typeof loader>();
  
  // 安全的数据处理
  const userTypeData = [
    { name: "会员用户", value: stats.memberUsers || 0, color: "#f59e0b" },
    { name: "普通用户", value: Math.max(0, (stats.totalUsers || 0) - (stats.memberUsers || 0)), color: "#6b7280" },
  ];
  
  const pluginTypeData = [
    { name: "推荐插件", value: stats.recommendedPlugins || 0, color: "#10b981" },
    { name: "普通插件", value: Math.max(0, (stats.totalPlugins || 0) - (stats.recommendedPlugins || 0)), color: "#6b7280" },
  ];
  
  const paidVsFreeData = [
    { name: "付费插件", value: stats.paidPlugins || 0, color: "#8b5cf6" },
    { name: "免费插件", value: stats.freePlugins || 0, color: "#06b6d4" },
  ];

  // 安全获取排行榜用户
  const topUsers = stats.searchStats?.topSearchUsers || [];
  const maxSearchCount = topUsers[0]?.searchCount || 1;

  // 自定义 Tooltip 组件
  const CustomTooltip = ({ active, payload, label, labelFormatter, formatter }: any) => {
    if (active && payload && payload.length) {
      return (
        <div className="bg-background border border-border rounded-lg shadow-lg p-3">
          <p className="text-sm font-medium">
            {labelFormatter ? labelFormatter(label) : label}
          </p>
          {payload.map((entry: any, index: number) => (
            <p key={index} className="text-sm" style={{ color: entry.color }}>
              {formatter ? formatter(entry.value, entry.name) : `${entry.name}: ${entry.value}`}
            </p>
          ))}
        </div>
      );
    }
    return null;
  };

  return (
    <div className="space-y-6">
      {/* 核心指标卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总用户数</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalUsers || 0}</div>
            <p className="text-xs text-muted-foreground">
              其中 {stats.activeUsers || 0} 位活跃用户
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总插件数</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalPlugins || 0}</div>
            <p className="text-xs text-muted-foreground">
              {stats.recommendedPlugins || 0} 个推荐插件
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总搜索次数</CardTitle>
            <Search className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.searchStats?.totalSearches || 0}</div>
            <p className="text-xs text-muted-foreground">
              平均每用户 {stats.searchStats?.averageSearchesPerUser || 0} 次
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">会员转化率</CardTitle>
            <Crown className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">
              {stats.totalUsers > 0 ? Math.round(((stats.memberUsers || 0) / stats.totalUsers) * 100) : 0}%
            </div>
            <p className="text-xs text-muted-foreground">
              {stats.memberUsers || 0} / {stats.totalUsers || 0} 用户
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 增长趋势图表 */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>用户增长趋势</CardTitle>
            <CardDescription>最近 30 天新注册用户数量</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={stats.usersGrowth || []}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="date" 
                  tickFormatter={(value) => new Date(value).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })}
                />
                <YAxis />
                <Tooltip 
                  content={<CustomTooltip 
                    labelFormatter={(value: string) => new Date(value).toLocaleDateString('zh-CN')}
                    formatter={(value: any, name: any) => [`${value} 个`, '新用户']}
                  />}
                />
                <Area 
                  type="monotone" 
                  dataKey="count" 
                  stroke="#3b82f6" 
                  fill="#3b82f6" 
                  fillOpacity={0.3}
                  name="新用户数"
                />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>插件发布趋势</CardTitle>
            <CardDescription>最近 30 天新发布插件数量</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={stats.pluginsGrowth || []}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="date"
                  tickFormatter={(value) => new Date(value).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })}
                />
                <YAxis />
                <Tooltip 
                  content={<CustomTooltip 
                    labelFormatter={(value: string) => new Date(value).toLocaleDateString('zh-CN')}
                    formatter={(value: any, name: any) => [`${value} 个`, '新插件']}
                  />}
                />
                <Line 
                  type="monotone" 
                  dataKey="count" 
                  stroke="#10b981" 
                  strokeWidth={2}
                  dot={{ fill: '#10b981' }}
                  name="新插件数"
                />
              </LineChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* 饼图和柱状图 */}
      <div className="grid gap-4 md:grid-cols-3">
        <Card>
          <CardHeader>
            <CardTitle>用户类型分布</CardTitle>
            <CardDescription>会员用户占比</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={200}>
              <PieChart>
                <Pie
                  data={userTypeData}
                  cx="50%"
                  cy="50%"
                  innerRadius={40}
                  outerRadius={80}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {userTypeData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip 
                  content={<CustomTooltip 
                    formatter={(value: any, name: any) => [`${value} 人`, name]}
                  />}
                />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>插件推荐分布</CardTitle>
            <CardDescription>推荐插件占比</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={200}>
              <PieChart>
                <Pie
                  data={pluginTypeData}
                  cx="50%"
                  cy="50%"
                  innerRadius={40}
                  outerRadius={80}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {pluginTypeData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip 
                  content={<CustomTooltip 
                    formatter={(value: any, name: any) => [`${value} 个`, name]}
                  />}
                />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>付费模式分布</CardTitle>
            <CardDescription>付费 vs 免费插件</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={200}>
              <PieChart>
                <Pie
                  data={paidVsFreeData}
                  cx="50%"
                  cy="50%"
                  innerRadius={40}
                  outerRadius={80}
                  paddingAngle={5}
                  dataKey="value"
                >
                  {paidVsFreeData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip 
                  content={<CustomTooltip 
                    formatter={(value: any, name: any) => [`${value} 个`, name]}
                  />}
                />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      </div>

      {/* 分类统计和用户排行 */}
      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>插件分类统计</CardTitle>
            <CardDescription>各分类的插件数量分布</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={stats.categoryStats || []}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="name" 
                  tick={{ fontSize: 12 }}
                  interval={0}
                  angle={-45}
                  textAnchor="end"
                  height={60}
                />
                <YAxis />
                <Tooltip 
                  content={<CustomTooltip 
                    formatter={(value: any, name: any) => [`${value} 个`, '插件数量']}
                  />}
                />
                <Bar dataKey="count" fill="#3b82f6" name="插件数量" />
              </BarChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>活跃用户排行榜</CardTitle>
            <CardDescription>搜索次数最多的用户</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topUsers.length > 0 ? (
                topUsers.slice(0, 8).map((user, index) => (
                  <div key={user.name} className="flex items-center space-x-4">
                    <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center text-sm font-medium">
                      {index + 1}
                    </div>
                    <div className="flex-1">
                      <p className="text-sm font-medium">{user.name}</p>
                      <Progress 
                        value={(user.searchCount / maxSearchCount) * 100} 
                        className="h-2 mt-1"
                      />
                    </div>
                    <Badge variant="secondary">{user.searchCount} 次</Badge>
                  </div>
                ))
              ) : (
                <div className="text-center text-muted-foreground py-8">
                  暂无活跃用户数据
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 系统概览 */}
      <div className="grid gap-4 md:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">插件分类</CardTitle>
            <Layers className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalCategories || 0}</div>
            <p className="text-xs text-muted-foreground">插件分类总数</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">管理员数量</CardTitle>
            <Shield className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.adminUsers || 0}</div>
            <p className="text-xs text-muted-foreground">系统管理员</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">当前预定会员数量</CardTitle>
            <UserPlus className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.totalReservedMembers || 0}</div>
            <p className="text-xs text-muted-foreground">预定会员资格</p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">用户活跃度</CardTitle>
            <Activity className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">
              {stats.totalUsers > 0 ? Math.round(((stats.activeUsers || 0) / stats.totalUsers) * 100) : 0}%
            </div>
            <p className="text-xs text-muted-foreground">
              {stats.activeUsers || 0} / {stats.totalUsers || 0} 活跃
            </p>
          </CardContent>
        </Card>
      </div>

      {/* 推荐插件趋势 */}
      {stats.recommendationTrends && stats.recommendationTrends.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>推荐插件发布趋势</CardTitle>
            <CardDescription>最近 30 天推荐插件的发布情况</CardDescription>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={200}>
              <AreaChart data={stats.recommendationTrends}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="date"
                  tickFormatter={(value) => new Date(value).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })}
                />
                <YAxis />
                <Tooltip 
                  content={<CustomTooltip 
                    labelFormatter={(value: string) => new Date(value).toLocaleDateString('zh-CN')}
                    formatter={(value: any, name: any) => [`${value} 个`, '推荐插件']}
                  />}
                />
                <Area 
                  type="monotone" 
                  dataKey="count" 
                  stroke="#f59e0b" 
                  fill="#f59e0b" 
                  fillOpacity={0.3}
                  name="推荐插件数"
                />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>
      )}
    </div>
  );
}