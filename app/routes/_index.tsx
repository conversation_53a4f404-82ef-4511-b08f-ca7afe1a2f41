import { useNavigate, useRouteLoaderData } from "react-router";
import type { Route } from "./+types/_index";
import type { User } from "database/schema";
import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import ky from "ky";
import { Button } from "~/components/ui/button";
import { LogOutIcon } from "lucide-react";
import { Cursor, CursorFollow, CursorProvider } from "~/components/ui/cursor";

export function meta({ }: Route.MetaArgs) {
  return [
    { title: "Plugmate 觅件" },
    { name: "description", content: "不断扩展的设计资产和 Figma 插件库" },
  ];
}

export async function action({ request, context }: Route.ActionArgs) {
}

export async function loader({ context }: Route.LoaderArgs) {
  return {};
}

export default function Home({ actionData, loaderData }: Route.ComponentProps) {
  const rootLoaderData = useRouteLoaderData<{ user: User }>("root");
  const navigate = useNavigate();
  const user = rootLoaderData?.user;
  const handleLogout = async () => {
    const response = await ky.get("/api/logout")
    if (response.ok) {
      navigate("/", { replace: true, viewTransition: true });
    }
  }

  return (
    <>
      <div className="flex flex-col gap-4 items-center justify-center h-screen relative">
        <h1 className="text-4xl font-bold flex gap-4 items-center"><img src="/android-chrome-512x512.png" alt="PlugMate" className="size-12" /><span>PlugMate</span></h1>
        <h2 className="text-2xl font-medium">不断扩展的设计资产和插件库</h2>
        <CursorProvider>
          <Cursor>
            <svg
              className="size-6 text-blue-500"
              xmlns="http://www.w3.org/2000/svg"
              viewBox="0 0 40 40"
            >
              <path
                fill="currentColor"
                d="M1.8 4.4 7 36.2c.3 1.8 2.6 2.3 3.6.8l3.9-5.7c1.7-2.5 4.5-4.1 7.5-4.3l6.9-.5c1.8-.1 2.5-2.4 1.1-3.5L5 2.5c-1.4-1.1-3.5 0-3.3 1.9Z"
              />
            </svg>
          </Cursor>
          <CursorFollow>
            <div className="bg-blue-500 text-white px-2 py-1 rounded-lg text-sm shadow-lg">
              正在开发中...
            </div>
          </CursorFollow>
        </CursorProvider>
        {user && <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm absolute top-4 right-4">
          <Avatar className="h-8 w-8 rounded-lg grayscale">
            <AvatarImage src={user.figmaImageUrl || undefined} alt={user.name} />
            <AvatarFallback className="rounded-lg">{user.name.slice(0, 2)}</AvatarFallback>
          </Avatar>
          <div className="grid text-left text-sm leading-tight">
            <span className="truncate font-medium">{user.name}</span>
            <span className="truncate text-xs text-muted-foreground">
              {user.email}
            </span>
          </div>
          <Button variant="ghost" size="icon" title="退出登录" onClick={handleLogout}>
            <LogOutIcon className="h-4 w-4" />
          </Button>
        </div>}
      </div>
    </>
  );
}
