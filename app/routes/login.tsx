import { LoginForm } from "~/components/login-form";
import type { Route } from "./+types/login";

export function meta({ }: Route.MetaArgs) {
  return [
    { title: "登录 | Plugmate" },
    { name: "description", content: "使用 Figma 一键登录你的账号" },
  ];
}

export async function loader({ context, request }: Route.LoaderArgs) {
  const url = new URL(request.url);
  const clientState = url.searchParams.get("state");

  return {
    figmaClientId: context.cloudflare.env.FIGMA_CLIENT_ID,
    figmaClientSecret: context.cloudflare.env.FIGMA_CLIENT_SECRET,
    figmaRedirectUri: context.cloudflare.env.FIGMA_REDIRECT_URI,
    clientState: clientState || undefined,
  };
}

export default function Login({ loaderData }: Route.ComponentProps) {
  const { figmaClientId, figmaClientSecret, figmaRedirectUri, clientState } = loaderData;

  return (
    <div className="flex flex-col items-center justify-center h-screen">
      <LoginForm
        figmaClientId={figmaClientId}
        figmaRedirectUri={figmaRedirectUri}
        figmaClientSecret={figmaClientSecret}
        clientState={clientState}
      />
    </div>
  );
}
