import { Outlet } from "react-router";
import { AppSidebar } from "~/components/app-sidebar";
import { SiteHeader } from "~/components/site-header";
import { SidebarInset, SidebarProvider } from "~/components/ui/sidebar";
import type { Route } from "./+types/admin";
import { requireAdminSession } from "~/session.server";

export function meta({ }: Route.MetaArgs) {
  return [
    { title: "后台管理 | Plugmate" },
    { name: "description", content: "管理插件、会员、用户等数据" },
  ];
}

export async function loader({ request, context }: Route.LoaderArgs) {
  const user = await requireAdminSession(request, context.cloudflare.env.AUTH_SECRET);

  return { user };
}

export default function Admin() {
  return (
    <SidebarProvider>
      <AppSidebar variant="inset" />
      <SidebarInset>
        <SiteHeader />
        <div className="flex flex-1 flex-col">
          <div className="@container/main flex flex-1 flex-col gap-2">
            <div className="flex flex-col gap-4 p-4 md:gap-6 md:p-6">
              <Outlet />
            </div>
          </div>
        </div>
      </SidebarInset>
    </SidebarProvider>
  );
}