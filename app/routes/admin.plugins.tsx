import type { Route } from "./+types/admin.plugins";
import { useF<PERSON>cher, useLoaderData } from "react-router";
import { useState, useEffect, useRef } from "react";
import type { DateRange } from "react-day-picker";
import { Button } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
// import { Checkbox } from "~/components/ui/checkbox";
import { Switch } from "~/components/ui/switch";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "~/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogT<PERSON>le,
  AlertDialogTrigger,
} from "~/components/ui/alert-dialog";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "~/components/ui/select";
import { Badge } from "~/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "~/components/ui/tooltip";
import { Search, Plus, Edit, Trash2, ImageUp, X, CalendarIcon, ShieldCheck, ShieldOff, TrendingUp } from "lucide-react";
import { toast } from "sonner";
import type { Plugin as DbPlugin, Category } from "../../database/schema";
import { requireAdminSession } from "~/session.server";
import { getPluginsList, getPluginsStats } from "~/lib/plugins.server";
import { getCategoriesList } from "~/lib/categories.server";
import { Popover, PopoverContent, PopoverTrigger } from "~/components/ui/popover";
import { Calendar } from "~/components/ui/calendar";
import { format } from "date-fns";
import { zhCN } from "date-fns/locale";
import { cn } from "~/lib/utils";
import { Slider } from "~/components/ui/slider";
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "~/components/ui/pagination";

interface PluginWithCategoryName extends DbPlugin {
  categoryName?: string;
}

// Define props for the new PluginForm component
interface PluginFormProps {
  plugin?: PluginWithCategoryName | null;
  categoriesList: Category[];
  fetcher: ReturnType<typeof useFetcher>;
  onClose: () => void;
  logoInputRef: React.RefObject<HTMLInputElement | null>;
  coversInputRef: React.RefObject<HTMLInputElement | null>;
  logoPreview: string | null;
  setLogoPreview: React.Dispatch<React.SetStateAction<string | null>>;
  coversPreview: string[];
  setCoversPreview: React.Dispatch<React.SetStateAction<string[]>>;
  existingCovers: string[];
  setExistingCovers: React.Dispatch<React.SetStateAction<string[]>>;
  deletedCoverIndices: number[];
  removeCover: (index: number, isExisting: boolean) => void;
  handleLogoChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  handleCoversChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
}

// Create the PluginForm component
const PluginForm: React.FC<PluginFormProps> = ({
  plugin,
  categoriesList,
  fetcher,
  onClose,
  logoInputRef,
  coversInputRef,
  logoPreview,
  setLogoPreview,
  coversPreview,
  existingCovers,
  deletedCoverIndices,
  removeCover,
  handleLogoChange,
  handleCoversChange,
}) => {
  const isEditing = !!plugin;
  const defaultValues = {
    name: plugin?.name || "",
    categoryId: plugin?.categoryId?.toString() || (categoriesList.length > 0 ? categoriesList[0].id.toString() : ""),
    url: plugin?.url || "",
    description: plugin?.description || "",
    isRecommended: plugin?.isRecommended === 1,
    recommendScore: plugin?.recommendScore || 0,
    recommendStartTime: plugin?.recommendStartTime ? new Date(plugin.recommendStartTime) : undefined,
    recommendEndTime: plugin?.recommendEndTime ? new Date(plugin.recommendEndTime) : undefined,
    isPaid: plugin?.isPaid === 1,
    author: plugin?.author || "",
  };
  const [dateRange, setDateRange] = useState<DateRange | undefined>({
    from: defaultValues.recommendStartTime,
    to: defaultValues.recommendEndTime,
  });
  const isLoading = fetcher.state !== "idle";
  const [currentRecommendScore, setCurrentRecommendScore] = useState(defaultValues.recommendScore);
  const [currentIsRecommended, setCurrentIsRecommended] = useState(defaultValues.isRecommended);
  const [currentIsPaid, setCurrentIsPaid] = useState(defaultValues.isPaid);

  return (
    <fetcher.Form method="post" action="/api/plugins" encType="multipart/form-data" className="space-y-4">
      <input type="hidden" name="_action" value={isEditing ? "update" : "create"} />
      {isEditing && plugin && <input type="hidden" name="id" value={plugin.id} />}

      <div className="grid grid-cols-4 gap-4">
        <div className="space-y-2 col-span-2">
          <Label htmlFor="name">名称 *</Label>
          <Input id="name" name="name" placeholder="插件名称" defaultValue={defaultValues.name} required />
        </div>
        <div className="space-y-2">
          <Label htmlFor="categoryId">分类 *</Label>
          <Select name="categoryId" defaultValue={defaultValues.categoryId} required>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="选择分类" />
            </SelectTrigger>
            <SelectContent>
              {categoriesList.map(cat => (
                <SelectItem
                  key={cat.id}
                  value={cat.id.toString()}
                  disabled={cat.disabled === 1}
                >{cat.name}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="space-y-2">
          <Label htmlFor="author">作者 (可选)</Label>
          <Input id="author" name="author" placeholder="插件作者" defaultValue={defaultValues.author} />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="url">网址 *</Label>
        <Input id="url" name="url" type="url" placeholder="https://example.com" defaultValue={defaultValues.url} required />
      </div>

      <div className="space-y-2">
        <Label htmlFor="description">介绍 *</Label>
        <Textarea id="description" name="description" placeholder="插件详细介绍" defaultValue={defaultValues.description} required />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 items-start">
        <div className="space-y-2">
          <Label htmlFor="logo">Logo*</Label>
          <Input 
            required={!isEditing || !plugin?.logo} 
            id="logo" 
            name="logo" 
            type="file" 
            accept="image/*" 
            onChange={handleLogoChange} 
            ref={logoInputRef} 
          />
          {isEditing && plugin?.logo && <input type="hidden" name="existingLogoKey" value={plugin.logo} />}
          {(logoPreview || (isEditing && plugin?.logo)) && (
            <div className="mt-2 relative w-32 h-32 border rounded">
              <img src={logoPreview || (isEditing && plugin?.logo ? plugin.logo.startsWith("https://") ? plugin.logo : `/r2/${plugin.logo}` : undefined)} alt="Logo Preview" className="object-contain w-full h-full" />
              {logoPreview && <Button type="button" variant="ghost" size="icon" className="absolute top-0 right-0" onClick={() => { setLogoPreview(null); if (logoInputRef.current) logoInputRef.current.value = ""; }}><X className="h-4 w-4" /></Button>}
            </div>
          )}
        </div>

        <div className="space-y-2">
          <Label htmlFor="covers">封面 * (可多图)</Label>
          <Input 
            required={!isEditing || !plugin?.covers} 
            id="covers" 
            name="covers" 
            type="file" 
            accept="image/*" 
            multiple 
            onChange={handleCoversChange} 
            ref={coversInputRef} 
          />
          {isEditing && plugin?.covers && <input type="hidden" name="existingCoversKeys" value={plugin.covers} />}
          {isEditing && <input type="hidden" name="deletedCoverIndices" value={JSON.stringify(deletedCoverIndices)} />}
          <div className="mt-2 flex flex-wrap gap-2">
            {existingCovers.map((src, index) => (
              <div key={`existing-${index}`} className="relative w-32 h-32 border rounded">
                <img src={src} alt={`Cover Preview ${index + 1}`} className="object-contain w-full h-full" />
                <Button type="button" variant="destructive" size="icon" className="absolute top-0 right-0" onClick={() => removeCover(index, true)}><X className="h-4 w-4" /></Button>
              </div>
            ))}
            {coversPreview.map((src, index) => (
              <div key={`new-${index}`} className="relative w-32 h-32 border rounded">
                <img src={src} alt={`New Cover Preview ${index + 1}`} className="object-contain w-full h-full" />
                <Button type="button" variant="ghost" size="icon" className="absolute top-0 right-0" onClick={() => removeCover(index, false)}><X className="h-4 w-4" /></Button>
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="space-y-2">
        <div className="flex flex-row items-center justify-between">
          <div className="space-y-1">
            <Label htmlFor="isPaid">是否为付费插件</Label>
            <p className="text-xs text-muted-foreground">
              标记插件是否需要付费使用。
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <Switch
              id="isPaid"
              name="isPaid"
              defaultChecked={currentIsPaid}
              onCheckedChange={setCurrentIsPaid}
            />
          </div>
        </div>
      </div>

      <div className="space-y-2">
        <div className="flex flex-row items-center justify-between">
          <div className="space-y-1">
            <Label htmlFor="isRecommended">是否推荐</Label>
            <p className="text-xs text-muted-foreground">
              推荐插件会显示在搜索结果页头部，并会根据推荐分数排序。
            </p>
          </div>
          <div className="flex items-center space-x-2">
            {/* <Checkbox id="isRecommended" name="isRecommended" defaultChecked={defaultValues.isRecommended} /> */}
            <Switch
              id="isRecommended"
              name="isRecommended"
              defaultChecked={currentIsRecommended}
              onCheckedChange={(checked) => {
                setCurrentIsRecommended(checked);
              }}
            />
            {/* <Label htmlFor="isRecommended" className="hidden">是否推荐</Label> */}
          </div>
        </div>
      </div>

      {currentIsRecommended && <>
        <div className="space-y-2">
          <Label htmlFor="recommendScore" className="mb-4">推荐分数 (越大越靠前)</Label>
          {/* <Input id="recommendScore" name="recommendScore" type="number" defaultValue={defaultValues.recommendScore} /> */}
          <Slider
            id="recommendScore"
            name="recommendScore"
            defaultValue={[defaultValues.recommendScore]}
            value={currentRecommendScore !== undefined ? [currentRecommendScore] : [defaultValues.recommendScore]}
            max={100}
            step={1}
            min={0}
            className="w-full"
            onValueChange={(value) => setCurrentRecommendScore(value[0])}
          />
          <div className="text-sm text-muted-foreground">当前：{currentRecommendScore !== undefined ? currentRecommendScore : defaultValues.recommendScore}</div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="recommendDateRange">推荐时间范围</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                id="recommendDateRange"
                variant={"outline"}
                className={cn(
                  "w-full justify-start text-left font-normal",
                  !dateRange?.from && "text-muted-foreground"
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {dateRange?.from ? (
                  dateRange.to ? (
                    <>
                      {format(dateRange.from, "yyyy-MM-dd", { locale: zhCN })} -{" "}
                      {format(dateRange.to, "yyyy-MM-dd", { locale: zhCN })}
                    </>
                  ) : (
                    format(dateRange.from, "yyyy-MM-dd", { locale: zhCN })
                  )
                ) : (
                  <span>选择日期范围</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                initialFocus
                mode="range"
                defaultMonth={dateRange?.from}
                selected={dateRange}
                onSelect={setDateRange}
                numberOfMonths={2}
              />
            </PopoverContent>
          </Popover>
          <input type="hidden" name="recommendStartTime" value={dateRange?.from ? dateRange.from.toISOString() : ""} />
          <input type="hidden" name="recommendEndTime" value={dateRange?.to ? dateRange.to.toISOString() : ""} />
        </div>
      </>}

      <DialogFooter>
        <Button type="button" variant="outline" onClick={onClose}>取消</Button>
        <Button type="submit" disabled={isLoading}>{isLoading ? "处理中..." : (isEditing ? "保存更改" : "创建插件")}</Button>
      </DialogFooter>
    </fetcher.Form>
  );
};

export function meta({ }: Route.MetaArgs) {
  return [
    { title: "插件管理 | Plugmate" },
    { name: "description", content: "插件管理" },
  ];
}

export async function loader({ request, context }: Route.LoaderArgs) {
  await requireAdminSession(request, context.cloudflare.env.AUTH_SECRET);
  const url = new URL(request.url);
  const search = url.searchParams.get("search") || "";
  const categoryId = url.searchParams.get("categoryId") || "all";
  const page = parseInt(url.searchParams.get("page") || "1");
  const pageSize = 10; // Or make this configurable

  const [{ plugins, totalPlugins }, stats, categoriesData] = await Promise.all([
    getPluginsList(context.db, search, categoryId, page, pageSize),
    getPluginsStats(context.db),
    getCategoriesList(context.db)
  ]);

  const totalPages = Math.ceil(totalPlugins / pageSize);

  return {
    plugins: plugins as PluginWithCategoryName[],
    stats,
    categories: categoriesData,
    search,
    categoryId,
    currentPage: page,
    totalPages,
  };
}

export default function AdminPlugins() {
  const data = useLoaderData<typeof loader>();
  const fetcher = useFetcher();
  const [search, setSearch] = useState(data.search);
  const [selectedCategoryId, setSelectedCategoryId] = useState(data.categoryId);
  const { currentPage, totalPages } = data;

  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [editingPlugin, setEditingPlugin] = useState<PluginWithCategoryName | null>(null);

  const logoInputRef = useRef<HTMLInputElement | null>(null);
  const coversInputRef = useRef<HTMLInputElement | null>(null);

  const [logoPreview, setLogoPreview] = useState<string | null>(null);
  const [coversPreview, setCoversPreview] = useState<string[]>([]);
  const [existingCovers, setExistingCovers] = useState<string[]>([]);
  const [deletedCoverIndices, setDeletedCoverIndices] = useState<number[]>([]);

  useEffect(() => {
    if (fetcher.data) {
      const response = fetcher.data as { success: boolean; message?: string; error?: string };
      if (response.success) {
        toast.success(response.message || "操作成功");
        setCreateDialogOpen(false);
        setEditDialogOpen(false);
        setEditingPlugin(null);
        setLogoPreview(null);
        setCoversPreview([]);
        setExistingCovers([]);
        setDeletedCoverIndices([]);
        if (logoInputRef.current) logoInputRef.current.value = "";
        if (coversInputRef.current) coversInputRef.current.value = "";
      } else if (response.error) {
        toast.error(response.error);
      }
    }
  }, [fetcher.data]);

  const handleSearch = (pageNumber?: number) => {
    const url = new URL(window.location.href);
    if (search) url.searchParams.set("search", search);
    else url.searchParams.delete("search");
    if (selectedCategoryId && selectedCategoryId !== "all") url.searchParams.set("categoryId", selectedCategoryId);
    else url.searchParams.delete("categoryId");

    // Reset to page 1 if it's a new search/filter, otherwise use provided page number or current page
    if (pageNumber) {
      url.searchParams.set("page", pageNumber.toString());
    } else {
      url.searchParams.set("page", "1"); // Always go to page 1 for new search/filter
    }
    window.location.href = url.toString();
  };

  const handleEdit = (plugin: PluginWithCategoryName) => {
    setEditingPlugin(plugin);
    if (plugin.logo) setLogoPreview(plugin.logo.startsWith("https://") ? plugin.logo : `/r2/${plugin.logo}`);
    else setLogoPreview(null);
    const currentCovers = plugin.covers ? JSON.parse(plugin.covers) : [];
    setExistingCovers(currentCovers.map((key: string) => key.startsWith("https://") ? key : `/r2/${key}`));
    setCoversPreview([]);
    setDeletedCoverIndices([]);
    setEditDialogOpen(true);
  };

  const handleLogoChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      const file = event.target.files[0];
      setLogoPreview(URL.createObjectURL(file));
    }
  };

  const handleCoversChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      const filesArray = Array.from(event.target.files);
      const newPreviews = filesArray.map(file => URL.createObjectURL(file));
      setCoversPreview(prev => [...prev, ...newPreviews]);
    }
  };

  const removeCover = (index: number, isExisting: boolean) => {
    if (isExisting) {
      setDeletedCoverIndices(prev => [...prev, index]);
      setExistingCovers(prev => prev.filter((_, i) => i !== index));
    } else {
      setCoversPreview(prev => prev.filter((_, i) => i !== index));
      if (coversInputRef.current) {
        const dt = new DataTransfer();
        const files = Array.from(coversInputRef.current.files || []);
        files.filter((_, i) => i !== index).forEach(file => dt.items.add(file));
        coversInputRef.current.files = dt.files;
      }
    }
  };

  const isLoading = fetcher.state !== "idle";
  const pluginsList = data.plugins;
  const categoriesList = data.categories as Category[];
  const stats = data.stats;

  return (
    <div className="space-y-6">
      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总插件数</CardTitle>
            <ImageUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalPlugins}</div>
            <p className="text-xs text-muted-foreground">当前已创建的插件总数</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">推荐插件</CardTitle>
            <TrendingUp className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{stats.recommendedPlugins}</div>
            <p className="text-xs text-muted-foreground">当前推荐的插件数量</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">普通插件</CardTitle>
            <ShieldCheck className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">{stats.normalPlugins}</div>
            <p className="text-xs text-muted-foreground">当前未被推荐的插件数量</p>
          </CardContent>
        </Card>
      </div>

      {/* 操作栏 */}
      <div className="flex items-center justify-between gap-2">
        <div className="flex items-center gap-2 flex-1 max-w-lg">
          <div className="relative flex-1">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索插件名称或介绍..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              onKeyDown={(e) => e.key === "Enter" && handleSearch()}
              className="pl-8"
            />
          </div>
          <Select value={selectedCategoryId} onValueChange={setSelectedCategoryId}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="所有分类" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">所有分类</SelectItem>
              {categoriesList.map(cat => (
                <SelectItem key={cat.id} value={cat.id.toString()}>{cat.name}</SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button onClick={() => handleSearch()} variant="secondary">
            搜索
          </Button>
        </div>

        <Dialog open={createDialogOpen} onOpenChange={(isOpen) => {
          setCreateDialogOpen(isOpen);
          if (!isOpen) {
            setLogoPreview(null);
            setCoversPreview([]);
            setExistingCovers([]);
            setDeletedCoverIndices([]);
            if (logoInputRef.current) logoInputRef.current.value = "";
            if (coversInputRef.current) coversInputRef.current.value = "";
          }
        }}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="size-4" />
              新增插件
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-3xl">
            <DialogHeader>
              <DialogTitle>新增插件</DialogTitle>
              <DialogDescription>
                填写插件的详细信息并上传相关图片。
              </DialogDescription>
            </DialogHeader>
            {/* Use PluginForm component for creating */}
            <PluginForm
              categoriesList={categoriesList}
              fetcher={fetcher}
              onClose={() => setCreateDialogOpen(false)}
              logoInputRef={logoInputRef}
              coversInputRef={coversInputRef}
              logoPreview={logoPreview}
              setLogoPreview={setLogoPreview}
              coversPreview={coversPreview}
              setCoversPreview={setCoversPreview}
              existingCovers={existingCovers} // Should be empty for create
              setExistingCovers={setExistingCovers} // Should not be needed for create
              deletedCoverIndices={deletedCoverIndices} // Should be empty for create
              removeCover={removeCover}
              handleLogoChange={handleLogoChange}
              handleCoversChange={handleCoversChange}
            />
          </DialogContent>
        </Dialog>
      </div>

      {/* 插件列表 */}
      <Card>
        <CardHeader>
          <CardTitle>插件列表</CardTitle>
          <CardDescription>管理您的插件，包括编辑和删除操作。</CardDescription>
        </CardHeader>
        <CardContent>
          <TooltipProvider>
            <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-16">Logo</TableHead>
                <TableHead className="min-w-[120px]">名称</TableHead>
                <TableHead className="min-w-[200px]">介绍</TableHead>
                <TableHead>分类</TableHead>
                <TableHead>URL</TableHead>
                <TableHead>作者</TableHead>
                <TableHead>付费</TableHead>
                <TableHead>推荐</TableHead>
                <TableHead className="w-24 text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {pluginsList.length === 0 && (
                <TableRow>
                  <TableCell colSpan={9} className="text-center h-24">
                    没有找到插件，请尝试调整搜索条件或新增插件。
                  </TableCell>
                </TableRow>
              )}
              {pluginsList.map((plugin) => (
                <TableRow key={plugin.id}>
                  <TableCell>
                    {plugin.logo ? (
                      <img src={plugin.logo.startsWith("https://") ? plugin.logo : `/r2/${plugin.logo}`} alt={plugin.name} className="h-10 w-10 object-contain rounded" />
                    ) : (
                      <div className="h-10 w-10 bg-muted rounded flex items-center justify-center">
                        <ImageUp className="h-5 w-5 text-muted-foreground" />
                      </div>
                    )}
                  </TableCell>
                  <TableCell className="font-medium">{plugin.name}</TableCell>
                  <TableCell className="max-w-[200px]">
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <div className="truncate cursor-help">
                          {plugin.description.length > 60 
                            ? `${plugin.description.substring(0, 60)}...` 
                            : plugin.description}
                        </div>
                      </TooltipTrigger>
                      <TooltipContent className="max-w-[300px] whitespace-pre-wrap">
                        <p>{plugin.description}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TableCell>
                  <TableCell>{plugin.categoryName || "N/A"}</TableCell>
                  <TableCell><a href={plugin.url} target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline truncate max-w-xs block">{plugin.url}</a></TableCell>
                  <TableCell>{plugin.author || "-"}</TableCell>
                  <TableCell>
                    {plugin.isPaid ? (
                      <Badge variant="outline">是</Badge>
                    ) : (
                      <Badge variant="secondary">否</Badge>
                    )}
                  </TableCell>
                  <TableCell>
                    {plugin.isRecommended ? (
                      <Badge variant="default" className="bg-green-600 hover:bg-green-700">{plugin.recommendScore} 分</Badge>
                    ) : (
                      <Badge variant="secondary">否</Badge>
                    )}
                  </TableCell>
                  <TableCell className="text-right">
                    <Dialog open={editDialogOpen && editingPlugin?.id === plugin.id} onOpenChange={(isOpen) => {
                      setEditDialogOpen(isOpen);
                      if (!isOpen) {
                        setEditingPlugin(null);
                        setLogoPreview(null);
                        setCoversPreview([]);
                        setExistingCovers([]);
                        setDeletedCoverIndices([]);
                        if (logoInputRef.current) logoInputRef.current.value = "";
                        if (coversInputRef.current) coversInputRef.current.value = "";
                      }
                    }}>
                      <DialogTrigger asChild>
                        <Button variant="ghost" size="icon" onClick={() => handleEdit(plugin)}>
                          <Edit className="h-4 w-4" />
                        </Button>
                      </DialogTrigger>
                      <DialogContent className="sm:max-w-3xl">
                        <DialogHeader>
                          <DialogTitle>编辑插件：{editingPlugin?.name}</DialogTitle>
                          <DialogDescription>
                            更新插件的详细信息和图片。
                          </DialogDescription>
                        </DialogHeader>
                        {/* Use PluginForm component for editing */}
                        {editingPlugin && (
                          <PluginForm
                            plugin={editingPlugin}
                            categoriesList={categoriesList}
                            fetcher={fetcher}
                            onClose={() => setEditDialogOpen(false)}
                            logoInputRef={logoInputRef}
                            coversInputRef={coversInputRef}
                            logoPreview={logoPreview}
                            setLogoPreview={setLogoPreview}
                            coversPreview={coversPreview}
                            setCoversPreview={setCoversPreview}
                            existingCovers={existingCovers}
                            setExistingCovers={setExistingCovers}
                            deletedCoverIndices={deletedCoverIndices}
                            removeCover={removeCover}
                            handleLogoChange={handleLogoChange}
                            handleCoversChange={handleCoversChange}
                          />
                        )}
                      </DialogContent>
                    </Dialog>

                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button variant="ghost" size="icon" className="text-red-600 hover:text-red-700">
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>确定删除插件？</AlertDialogTitle>
                          <AlertDialogDescription>
                            此操作无法撤销。这将永久删除插件 "{plugin.name}" 及其所有数据，包括上传的图片文件。
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>取消</AlertDialogCancel>
                          <fetcher.Form method="post" action="/api/plugins">
                            <input type="hidden" name="_action" value="delete" />
                            <input type="hidden" name="id" value={plugin.id.toString()} />
                            <AlertDialogAction type="submit" className="bg-red-600 hover:bg-red-700 text-white" disabled={isLoading}>
                              {isLoading ? "删除中..." : "确认删除"}
                            </AlertDialogAction>
                          </fetcher.Form>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          {totalPages > 1 && (
            <Pagination className="mt-4">
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    href={currentPage > 1 ? `?page=${currentPage - 1}&search=${search}&categoryId=${selectedCategoryId}` : undefined}
                    onClick={(e) => {
                      if (currentPage <= 1) e.preventDefault();
                      else handleSearch(currentPage - 1);
                    }}
                    aria-disabled={currentPage <= 1}
                    tabIndex={currentPage <= 1 ? -1 : undefined}
                    className={
                      currentPage <= 1 ? "pointer-events-none opacity-50" : undefined
                    }
                  />
                </PaginationItem>
                {getPaginationItems(currentPage, totalPages, search, selectedCategoryId).map((item, index) => (
                  <PaginationItem key={index}>
                    {item.type === "ellipsis" ? (
                      <PaginationEllipsis />
                    ) : (
                      <PaginationLink
                        href={`?page=${item.page}&search=${search}&categoryId=${selectedCategoryId}`}
                        onClick={(e) => {
                          e.preventDefault();
                          handleSearch(item.page as number);
                        }}
                        isActive={item.page === currentPage}
                      >
                        {item.page}
                      </PaginationLink>
                    )}
                  </PaginationItem>
                ))}
                <PaginationItem>
                  <PaginationNext
                    href={currentPage < totalPages ? `?page=${currentPage + 1}&search=${search}&categoryId=${selectedCategoryId}` : undefined}
                    onClick={(e) => {
                      if (currentPage >= totalPages) e.preventDefault();
                      else handleSearch(currentPage + 1);
                    }}
                    aria-disabled={currentPage >= totalPages}
                    tabIndex={currentPage >= totalPages ? -1 : undefined}
                    className={
                      currentPage >= totalPages ? "pointer-events-none opacity-50" : undefined
                    }
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          )}
          </TooltipProvider>
        </CardContent>
      </Card>
    </div>
  );
}

// Helper function to determine pagination items
function getPaginationItems(currentPage: number, totalPages: number, search: string, categoryId: string) {
  const delta = 2; // Number of pages to show before and after current page
  const range = [];

  // Always show first page
  range.push({ page: 1, type: "page" });

  // Ellipsis if needed after first page
  if (currentPage - delta > 2) {
    range.push({ type: "ellipsis" });
  }

  // Pages around current page
  for (let i = Math.max(2, currentPage - delta); i <= Math.min(totalPages - 1, currentPage + delta); i++) {
    range.push({ page: i, type: "page" });
  }

  // Ellipsis if needed before last page
  if (currentPage + delta < totalPages - 1) {
    range.push({ type: "ellipsis" });
  }

  // Always show last page if totalPages > 1
  if (totalPages > 1) {
    range.push({ page: totalPages, type: "page" });
  }

  // Deduplicate and ensure structure
  const uniqueRange: Array<{ page?: number, type: string }> = [];
  const seenPages = new Set<number>();

  for (const item of range) {
    if (item.type === "ellipsis") {
      if (uniqueRange.length > 0 && uniqueRange[uniqueRange.length - 1].type !== "ellipsis") {
        uniqueRange.push(item);
      }
    } else if (item.page && !seenPages.has(item.page)) {
      uniqueRange.push(item);
      seenPages.add(item.page);
    }
  }
  return uniqueRange;
}