import type { LoaderFunctionArgs } from "react-router";

export async function loader({ params, context }: LoaderFunctionArgs) {
  const { cloudflare } = context;
  const env = cloudflare.env;
  const objectKey = params.key;

  if (!objectKey) {
    return new Response("File key not provided", { status: 400 });
  }

  try {
    const object = await env.R2_BUCKET.get(objectKey);

    if (object === null) {
      return new Response("Object Not Found", { status: 404 });
    }

    const headers = new Headers();
    object.writeHttpMetadata(headers);
    headers.set("etag", object.httpEtag);
    // Consider adding Cache-Control headers for browser caching
    // headers.set("Cache-Control", "public, max-age=31536000"); 

    return new Response(object.body, {
      headers,
    });
  } catch (error) {
    console.error("Error fetching file from R2:", error);
    return new Response("Error fetching file", { status: 500 });
  }
} 