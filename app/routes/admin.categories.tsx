import type { Route } from "./+types/admin.categories";
import { use<PERSON><PERSON><PERSON>, use<PERSON>oaderData } from "react-router";
import { useState, useEffect } from "react";
import { But<PERSON> } from "~/components/ui/button";
import { Input } from "~/components/ui/input";
import { Label } from "~/components/ui/label";
import { Textarea } from "~/components/ui/textarea";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "~/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "~/components/ui/alert-dialog";
import { Badge } from "~/components/ui/badge";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Search, Plus, Edit, Ban, CheckCircle, LayoutDashboard } from "lucide-react";
import { toast } from "sonner";
import type { Category } from "database/schema";
import { requireAdminSession } from "~/session.server";
import { getCategoriesList } from "~/lib/categories.server";

export function meta({ }: Route.MetaArgs) {
  return [
    { title: "分类管理 | Plugmate" },
    { name: "description", content: "分类管理" },
  ];
}

export async function loader({ request, context }: Route.LoaderArgs) {
  await requireAdminSession(request, context.cloudflare.env.AUTH_SECRET);
  
  const url = new URL(request.url);
  const search = url.searchParams.get("search") || "";
  
  const categoriesData = await getCategoriesList(context.db, search);
  
  return { categories: categoriesData, search };
}

export default function AdminCategories() {
  const data = useLoaderData<typeof loader>();
  const fetcher = useFetcher();
  const [search, setSearch] = useState(data.search);
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [editingCategory, setEditingCategory] = useState<Category | null>(null);

  // 监听表单提交状态
  useEffect(() => {
    if (fetcher.data) {
      if (fetcher.data.success) {
        toast.success("操作成功");
        setCreateDialogOpen(false);
        setEditDialogOpen(false);
        setEditingCategory(null);
      } else if (fetcher.data.error) {
        toast.error(fetcher.data.error);
      }
    }
  }, [fetcher.data]);

  // 搜索处理
  const handleSearch = () => {
    const url = new URL(window.location.href);
    if (search) {
      url.searchParams.set("search", search);
    } else {
      url.searchParams.delete("search");
    }
    window.location.href = url.toString();
  };

  // 编辑分类
  const handleEdit = (category: Category) => {
    setEditingCategory(category);
    setEditDialogOpen(true);
  };

  // 切换分类状态（禁用/启用）
  const handleToggleStatus = (id: number, currentDisabled: number) => {
    const action = currentDisabled ? "enable" : "disable";
    fetcher.submit(
      { _action: action, id: id.toString() },
      { method: "post", action: "/api/categories" }
    );
  };

  const isLoading = fetcher.state !== "idle";
  const categories = data.categories;
  
  // 计算统计数据
  const activeCategories = categories.filter((cat: Category) => !cat.disabled);
  const disabledCategories = categories.filter((cat: Category) => cat.disabled);

  return (
    <div className="space-y-6">
      {/* 统计卡片 */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">总分类数</CardTitle>
            <LayoutDashboard className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{categories.length}</div>
            <p className="text-xs text-muted-foreground">当前已创建的分类总数</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">启用分类</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{activeCategories.length}</div>
            <p className="text-xs text-muted-foreground">当前启用的分类数量</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">禁用分类</CardTitle>
            <Ban className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{disabledCategories.length}</div>
            <p className="text-xs text-muted-foreground">当前禁用的分类数量</p>
          </CardContent>
        </Card>
      </div>

      {/* 操作栏 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2 flex-1 max-w-md">
          <div className="relative flex-1">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="搜索分类名称..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              onKeyDown={(e) => e.key === "Enter" && handleSearch()}
              className="pl-8"
            />
          </div>
          <Button onClick={handleSearch} variant="secondary">
            搜索
          </Button>
        </div>

        {/* 新增分类对话框 */}
        <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="size-4" />
              新增分类
            </Button>
          </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>新增分类</DialogTitle>
              <DialogDescription>
                创建一个新的插件分类，分类名称必须唯一。
              </DialogDescription>
            </DialogHeader>
            <fetcher.Form method="post" action="/api/categories" className="space-y-4">
              <input type="hidden" name="_action" value="create" />
              <div className="space-y-2">
                <Label htmlFor="name">分类名称 *</Label>
                <Input
                  id="name"
                  name="name"
                  placeholder="请输入分类名称"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="description">分类描述</Label>
                <Textarea
                  id="description"
                  name="description"
                  placeholder="请输入分类描述（可选）"
                  rows={3}
                />
              </div>
              <DialogFooter>
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? "创建中..." : "创建分类"}
                </Button>
              </DialogFooter>
            </fetcher.Form>
          </DialogContent>
        </Dialog>
      </div>

      {/* 分类列表 */}
      <Card>
        <CardHeader>
          <CardTitle>分类列表</CardTitle>
          <CardDescription>
            {data.search ? `搜索 "${data.search}" 的结果：${categories.length} 个分类` : `共 ${categories.length} 个分类`}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>ID</TableHead>
                  <TableHead>分类名称</TableHead>
                  <TableHead>描述</TableHead>
                  <TableHead>状态</TableHead>
                  <TableHead>创建时间</TableHead>
                  <TableHead>更新时间</TableHead>
                  <TableHead className="text-right">操作</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {categories.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center py-8 text-muted-foreground">
                      {data.search ? "未找到匹配的分类" : "暂无分类数据"}
                    </TableCell>
                  </TableRow>
                ) : (
                  categories.map((category: Category) => (
                    <TableRow key={category.id} className={category.disabled ? "opacity-60" : ""}>
                      <TableCell>
                        <Badge variant="secondary">{category.id}</Badge>
                      </TableCell>
                      <TableCell className="font-medium">{category.name}</TableCell>
                      <TableCell className="max-w-[200px] truncate">
                        {category.description || "-"}
                      </TableCell>
                      <TableCell>
                        <Badge variant={category.disabled ? "destructive" : "default"}>
                          {category.disabled ? "已禁用" : "已启用"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {new Date(category.createdAt).toLocaleDateString("zh-CN", {
                          year: "numeric",
                          month: "2-digit",
                          day: "2-digit",
                          hour: "2-digit",
                          minute: "2-digit",
                        })}
                      </TableCell>
                      <TableCell>
                        {new Date(category.updatedAt).toLocaleDateString("zh-CN", {
                          year: "numeric",
                          month: "2-digit",
                          day: "2-digit",
                          hour: "2-digit",
                          minute: "2-digit",
                        })}
                      </TableCell>
                      <TableCell className="text-right">
                        <div className="flex items-center justify-end gap-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEdit(category)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <AlertDialog>
                            <AlertDialogTrigger asChild>
                              <Button 
                                variant="outline" 
                                size="sm" 
                                className={category.disabled ? "text-green-600 hover:text-green-600" : "text-red-600 hover:text-red-600"}
                              >
                                {category.disabled ? <CheckCircle className="h-4 w-4" /> : <Ban className="h-4 w-4" />}
                              </Button>
                            </AlertDialogTrigger>
                            <AlertDialogContent>
                              <AlertDialogHeader>
                                <AlertDialogTitle>
                                  确认{category.disabled ? "启用" : "禁用"}分类
                                </AlertDialogTitle>
                                <AlertDialogDescription>
                                  您确定要{category.disabled ? "启用" : "禁用"}分类 "{category.name}" 吗？
                                  {category.disabled 
                                    ? "启用后，此分类将重新可用于插件分类。"
                                    : "禁用后，此分类将不再显示在前台，但已关联的插件不会受到影响。"
                                  }
                                </AlertDialogDescription>
                              </AlertDialogHeader>
                              <AlertDialogFooter>
                                <AlertDialogCancel>取消</AlertDialogCancel>
                                <AlertDialogAction
                                  onClick={() => handleToggleStatus(category.id, category.disabled)}
                                  className={category.disabled 
                                    ? "bg-green-600 text-white hover:bg-green-700" 
                                    : "bg-red-600 text-white hover:bg-red-700"
                                  }
                                >
                                  {category.disabled ? "启用" : "禁用"}
                                </AlertDialogAction>
                              </AlertDialogFooter>
                            </AlertDialogContent>
                          </AlertDialog>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* 编辑分类对话框 */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>编辑分类</DialogTitle>
            <DialogDescription>
              修改分类信息，分类名称必须唯一。
            </DialogDescription>
          </DialogHeader>
          {editingCategory && (
            <fetcher.Form method="post" action="/api/categories" className="space-y-4">
              <input type="hidden" name="_action" value="update" />
              <input type="hidden" name="id" value={editingCategory.id} />
              <div className="space-y-2">
                <Label htmlFor="edit-name">分类名称 *</Label>
                <Input
                  id="edit-name"
                  name="name"
                  placeholder="请输入分类名称"
                  defaultValue={editingCategory.name}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="edit-description">分类描述</Label>
                <Textarea
                  id="edit-description"
                  name="description"
                  placeholder="请输入分类描述（可选）"
                  defaultValue={editingCategory.description || ""}
                  rows={3}
                />
              </div>
              <DialogFooter>
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? "保存中..." : "保存更改"}
                </Button>
              </DialogFooter>
            </fetcher.Form>
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
} 