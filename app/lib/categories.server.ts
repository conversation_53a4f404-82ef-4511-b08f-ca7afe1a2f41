import { categories } from "database/schema";
import { like, asc } from "drizzle-orm";

// 使用通用的数据库类型，与项目中的 context.db 保持一致
export async function getCategoriesList(db: any, search?: string) {
  try {
    let categoriesData;
    
    if (search) {
      categoriesData = await db
        .select()
        .from(categories)
        .where(like(categories.name, `%${search}%`))
        .orderBy(asc(categories.createdAt));
    } else {
      categoriesData = await db
        .select()
        .from(categories)
        .orderBy(asc(categories.createdAt));
    }
    
    return categoriesData;
  } catch (error) {
    console.error("获取分类列表失败：", error);
    throw new Error("获取分类列表失败");
  }
} 