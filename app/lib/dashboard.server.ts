import { users, plugins, categories, reservedMembers } from "database/schema";
import { count, sql, desc, asc, eq, and, gte, lte } from "drizzle-orm";

export interface DashboardStats {
  // 总体统计
  totalUsers: number;
  totalPlugins: number;
  totalCategories: number;
  totalReservedMembers: number;

  // 用户统计
  memberUsers: number;
  adminUsers: number;
  activeUsers: number; // 有搜索记录的用户

  // 插件统计
  recommendedPlugins: number;
  paidPlugins: number;
  freePlugins: number;

  // 时间统计
  usersGrowth: Array<{ date: string; count: number }>;
  pluginsGrowth: Array<{ date: string; count: number }>;

  // 分类统计
  categoryStats: Array<{ name: string; count: number }>;

  // 搜索统计
  searchStats: {
    totalSearches: number;
    averageSearchesPerUser: number;
    topSearchUsers: Array<{ name: string; searchCount: number }>;
  };

  // 插件推荐趋势
  recommendationTrends: Array<{ date: string; count: number }>;
}

export async function getDashboardStats(db: any): Promise<DashboardStats> {
  try {
    const thirtyDaysAgo = Date.now() - (30 * 24 * 60 * 60 * 1000);

    // 并行执行所有查询，减少总的等待时间
    const [
      // 用户相关统计 - 合并为一个查询
      userStatsResult,
      // 插件相关统计 - 合并为一个查询  
      pluginStatsResult,
      // 其他基础统计
      categoriesCountResult,
      reservedMembersCountResult,
      // 时间趋势查询
      usersGrowthData,
      pluginsGrowthData,
      recommendationTrendsData,
      // 分类统计和用户排行
      categoryStatsData,
      topSearchUsers
    ] = await Promise.all([
      // 查询 1: 用户统计（合并 5 个用户相关查询为 1 个）
      db.select({
        totalUsers: count(),
        memberUsers: count(sql`CASE WHEN ${users.isMember} = 1 THEN 1 END`),
        adminUsers: count(sql`CASE WHEN ${users.role} = 'admin' THEN 1 END`),
        activeUsers: count(sql`CASE WHEN ${users.searchCount} > 0 THEN 1 END`),
        totalSearches: sql<number>`COALESCE(SUM(${users.searchCount}), 0)`
      }).from(users),

      // 查询 2: 插件统计（合并 3 个插件相关查询为 1 个）
      db.select({
        totalPlugins: count(),
        recommendedPlugins: count(sql`CASE WHEN ${plugins.isRecommended} = 1 THEN 1 END`),
        paidPlugins: count(sql`CASE WHEN ${plugins.isPaid} = 1 THEN 1 END`)
      }).from(plugins),

      // 查询 3: 分类总数
      db.select({ count: count() }).from(categories),

      // 查询 4: 预定会员总数
      db.select({ count: count() }).from(reservedMembers),

      // 查询 5: 用户增长趋势
      db.select({
        date: sql<string>`date(${users.createdAt} / 1000, 'unixepoch')`,
        count: count()
      })
        .from(users)
        .where(gte(users.createdAt, thirtyDaysAgo))
        .groupBy(sql`date(${users.createdAt} / 1000, 'unixepoch')`)
        .orderBy(sql`date(${users.createdAt} / 1000, 'unixepoch')`),

      // 查询 6: 插件增长趋势
      db.select({
        date: sql<string>`date(${plugins.createdAt} / 1000, 'unixepoch')`,
        count: count()
      })
        .from(plugins)
        .where(gte(plugins.createdAt, thirtyDaysAgo))
        .groupBy(sql`date(${plugins.createdAt} / 1000, 'unixepoch')`)
        .orderBy(sql`date(${plugins.createdAt} / 1000, 'unixepoch')`),

      // 查询 7: 推荐插件趋势
      db.select({
        date: sql<string>`date(${plugins.createdAt} / 1000, 'unixepoch')`,
        count: count()
      })
        .from(plugins)
        .where(
          and(
            gte(plugins.createdAt, thirtyDaysAgo),
            eq(plugins.isRecommended, 1)
          )
        )
        .groupBy(sql`date(${plugins.createdAt} / 1000, 'unixepoch')`)
        .orderBy(sql`date(${plugins.createdAt} / 1000, 'unixepoch')`),

      // 查询 8: 分类统计
      db.select({
        name: categories.name,
        count: count(plugins.id)
      })
        .from(categories)
        .leftJoin(plugins, eq(categories.id, plugins.categoryId))
        .groupBy(categories.id, categories.name)
        .orderBy(desc(count(plugins.id))),

      // 查询 9: 活跃用户排行
      db.select({
        name: users.name,
        searchCount: users.searchCount
      })
        .from(users)
        .where(sql`${users.searchCount} > 0`)
        .orderBy(desc(users.searchCount))
        .limit(10)
    ]);

    // 提取用户统计数据
    const userStats = userStatsResult[0];
    const totalUsers = Number(userStats?.totalUsers || 0);
    const memberUsers = Number(userStats?.memberUsers || 0);
    const adminUsers = Number(userStats?.adminUsers || 0);
    const activeUsers = Number(userStats?.activeUsers || 0);
    const totalSearches = Number(userStats?.totalSearches || 0);

    // 提取插件统计数据
    const pluginStats = pluginStatsResult[0];
    const totalPlugins = Number(pluginStats?.totalPlugins || 0);
    const recommendedPlugins = Number(pluginStats?.recommendedPlugins || 0);
    const paidPlugins = Number(pluginStats?.paidPlugins || 0);
    const freePlugins = totalPlugins - paidPlugins;

    // 提取其他统计数据
    const totalCategories = Number(categoriesCountResult[0]?.count || 0);
    const totalReservedMembers = Number(reservedMembersCountResult[0]?.count || 0);

    // 计算平均搜索次数
    const averageSearchesPerUser = totalUsers > 0 ? Math.round((totalSearches / totalUsers) * 100) / 100 : 0;

    return {
      totalUsers,
      totalPlugins,
      totalCategories,
      totalReservedMembers,
      memberUsers,
      adminUsers,
      activeUsers,
      recommendedPlugins,
      paidPlugins,
      freePlugins,
      usersGrowth: usersGrowthData || [],
      pluginsGrowth: pluginsGrowthData || [],
      categoryStats: categoryStatsData || [],
      searchStats: {
        totalSearches,
        averageSearchesPerUser,
        topSearchUsers: topSearchUsers || []
      },
      recommendationTrends: recommendationTrendsData || []
    };
  } catch (error) {
    console.error("获取数据看板统计失败：", error);

    // 如果数据库查询失败，返回空数据而不是抛出错误
    // 这样可以让页面仍然能显示，只是数据为空
    return {
      totalUsers: 0,
      totalPlugins: 0,
      totalCategories: 0,
      totalReservedMembers: 0,
      memberUsers: 0,
      adminUsers: 0,
      activeUsers: 0,
      recommendedPlugins: 0,
      paidPlugins: 0,
      freePlugins: 0,
      usersGrowth: [],
      pluginsGrowth: [],
      categoryStats: [],
      searchStats: {
        totalSearches: 0,
        averageSearchesPerUser: 0,
        topSearchUsers: []
      },
      recommendationTrends: []
    };
  }
}

// 辅助函数：为图表数据填充空白日期
export function fillMissingDates(
  data: Array<{ date: string; count: number }>,
  days: number = 30
): Array<{ date: string; count: number }> {
  const result: Array<{ date: string; count: number }> = [];
  const today = new Date();

  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);
    const dateString = date.toISOString().split('T')[0];

    const existingData = data.find(item => item.date === dateString);
    result.push({
      date: dateString,
      count: existingData?.count || 0
    });
  }

  return result;
}

// 辅助函数：生成示例数据（用于演示）
export function generateSampleDashboardData(): DashboardStats {
  const today = new Date();
  const sampleUsersGrowth = [];
  const samplePluginsGrowth = [];

  // 生成最近 30 天的示例数据
  for (let i = 29; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);
    const dateString = date.toISOString().split('T')[0];

    sampleUsersGrowth.push({
      date: dateString,
      count: Math.floor(Math.random() * 10) + 1
    });

    samplePluginsGrowth.push({
      date: dateString,
      count: Math.floor(Math.random() * 5)
    });
  }

  return {
    totalUsers: 156,
    totalPlugins: 89,
    totalCategories: 12,
    totalReservedMembers: 23,
    memberUsers: 34,
    adminUsers: 3,
    activeUsers: 78,
    recommendedPlugins: 25,
    paidPlugins: 15,
    freePlugins: 74,
    usersGrowth: sampleUsersGrowth,
    pluginsGrowth: samplePluginsGrowth,
    categoryStats: [
      { name: "设计工具", count: 25 },
      { name: "原型制作", count: 18 },
      { name: "图标资源", count: 15 },
      { name: "UI 套件", count: 12 },
      { name: "数据可视化", count: 8 },
      { name: "其他", count: 11 }
    ],
    searchStats: {
      totalSearches: 1247,
      averageSearchesPerUser: 8.0,
      topSearchUsers: [
        { name: "张三", searchCount: 45 },
        { name: "李四", searchCount: 38 },
        { name: "王五", searchCount: 32 },
        { name: "赵六", searchCount: 28 },
        { name: "陈七", searchCount: 25 },
        { name: "刘八", searchCount: 22 },
        { name: "杨九", searchCount: 18 },
        { name: "黄十", searchCount: 15 }
      ]
    },
    recommendationTrends: samplePluginsGrowth.map(item => ({
      ...item,
      count: Math.floor(item.count / 2)
    }))
  };
} 