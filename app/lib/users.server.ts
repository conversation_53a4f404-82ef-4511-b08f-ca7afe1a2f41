import { users, reservedMembers } from "database/schema";
import { like, asc, desc, eq, count, or } from "drizzle-orm";

export interface GetUsersParams {
  page?: number;
  pageSize?: number;
  search?: string;
  orderBy?: 'createdAt' | 'name' | 'email';
  orderDirection?: 'asc' | 'desc';
}

export async function getUsersList(db: any, params: GetUsersParams = {}) {
  try {
    const {
      page = 1,
      pageSize = 10,
      search = "",
      orderBy = 'createdAt',
      orderDirection = 'desc'
    } = params;

    const offset = (page - 1) * pageSize;

    // 构建查询条件 - 支持按名称和邮箱搜索
    let whereCondition;
    if (search) {
      whereCondition = or(
        like(users.name, `%${search}%`),
        like(users.email, `%${search}%`)
      );
    }

    // 构建排序条件
    const orderCondition = orderDirection === 'asc' 
      ? asc(users[orderBy]) 
      : desc(users[orderBy]);

    // 获取总数
    const totalQuery = db
      .select({ count: count() })
      .from(users);
    
    if (whereCondition) {
      totalQuery.where(whereCondition);
    }
    
    const [{ count: total }] = await totalQuery;

    // 获取分页数据
    let usersQuery = db
      .select({
        id: users.id,
        name: users.name,
        email: users.email,
        figmaId: users.figmaId,
        figmaImageUrl: users.figmaImageUrl,
        role: users.role,
        isMember: users.isMember,
        searchCount: users.searchCount,
        createdAt: users.createdAt,
        updatedAt: users.updatedAt,
      })
      .from(users)
      .orderBy(orderCondition)
      .limit(pageSize)
      .offset(offset);

    if (whereCondition) {
      usersQuery = usersQuery.where(whereCondition);
    }

    const usersData = await usersQuery;

    return {
      users: usersData,
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize),
      }
    };
  } catch (error) {
    console.error("获取用户列表失败：", error);
    throw new Error("获取用户列表失败");
  }
}

export async function updateUserMembership(db: any, userId: number, isMember: boolean) {
  try {
    const [updatedUser] = await db
      .update(users)
      .set({
        isMember: isMember ? 1 : 0,
        updatedAt: Date.now(),
      })
      .where(eq(users.id, userId))
      .returning();

    return updatedUser;
  } catch (error) {
    console.error("更新用户会员状态失败：", error);
    throw new Error("更新用户会员状态失败");
  }
}

export async function updateUserRole(db: any, userId: number, role: string) {
  try {
    const [updatedUser] = await db
      .update(users)
      .set({
        role,
        updatedAt: Date.now(),
      })
      .where(eq(users.id, userId))
      .returning();

    return updatedUser;
  } catch (error) {
    console.error("更新用户角色失败：", error);
    throw new Error("更新用户角色失败");
  }
}

export async function findUserByEmail(db: any, email: string) {
  try {
    const user = await db
      .select()
      .from(users)
      .where(eq(users.email, email))
      .limit(1);

    return user[0] || null;
  } catch (error) {
    console.error("查找用户失败：", error);
    throw new Error("查找用户失败");
  }
}

export async function addReservedMember(db: any, email: string) {
  try {
    const [newReservedMember] = await db
      .insert(reservedMembers)
      .values({
        email,
        createdAt: Date.now(),
      })
      .returning();

    return newReservedMember;
  } catch (error) {
    console.error("添加预定会员失败：", error);
    throw new Error("添加预定会员失败");
  }
}

export async function checkReservedMemberExists(db: any, email: string) {
  try {
    const reservedMember = await db
      .select()
      .from(reservedMembers)
      .where(eq(reservedMembers.email, email))
      .limit(1);

    return reservedMember[0] || null;
  } catch (error) {
    console.error("检查预定会员失败：", error);
    throw new Error("检查预定会员失败");
  }
} 