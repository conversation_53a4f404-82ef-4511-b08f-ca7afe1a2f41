import { plugins, categories } from "database/schema";
import { like, asc, desc, eq, or, sql, count } from "drizzle-orm";

// 获取插件列表
export async function getPluginsList(db: any, search?: string, categoryId?: string, page: number = 1, pageSize: number = 15) {
  try {
    const whereClauses = [];
    if (search) {
      whereClauses.push(
        or(
          like(plugins.name, `%${search}%`),
          like(plugins.description, `%${search}%`)
        )
      );
    }
    if (categoryId && categoryId !== "all") {
      whereClauses.push(eq(plugins.categoryId, parseInt(categoryId)));
    }

    const whereCondition = whereClauses.length > 0 ? sql.join(whereClauses, sql` AND `) : undefined;

    const pluginsQuery = db
      .select({
        id: plugins.id,
        logo: plugins.logo,
        covers: plugins.covers,
        name: plugins.name,
        categoryId: plugins.categoryId,
        categoryName: categories.name,
        url: plugins.url,
        description: plugins.description,
        isRecommended: plugins.isRecommended,
        recommendScore: plugins.recommendScore,
        recommendStartTime: plugins.recommendStartTime,
        recommendEndTime: plugins.recommendEndTime,
        createdAt: plugins.createdAt,
        updatedAt: plugins.updatedAt,
        isPaid: plugins.isPaid,
        author: plugins.author,
      })
      .from(plugins)
      .leftJoin(categories, eq(plugins.categoryId, categories.id))
      .orderBy(desc(plugins.createdAt))
      .limit(pageSize)
      .offset((page - 1) * pageSize);

    if (whereCondition) {
      pluginsQuery.where(whereCondition);
    }

    const pluginsData = await pluginsQuery;

    const totalCountQuery = db.select({ count: count() }).from(plugins);
    if (whereCondition) {
      totalCountQuery.where(whereCondition);
    }
    const totalResult = await totalCountQuery;
    const totalPlugins = totalResult[0].count;

    return { plugins: pluginsData, totalPlugins };
  } catch (error) {
    console.error("获取插件列表失败：", error);
    throw new Error("获取插件列表失败");
  }
}

// 获取插件详情
export async function getPluginById(db: any, id: number) {
  try {
    const pluginData = await db
      .select()
      .from(plugins)
      .where(eq(plugins.id, id))
      .limit(1);

    // Ensure that the returned object matches the Plugin type, especially if using $inferSelect
    const result = pluginData[0] as (typeof plugins.$inferSelect) | null;
    return result;

  } catch (error) {
    console.error("获取插件详情失败：", error);
    throw new Error("获取插件详情失败");
  }
}

// 创建插件
export async function createPlugin(db: any, data: {
  logo: string;
  covers: string;
  name: string;
  categoryId: number;
  url: string;
  description: string;
  isRecommended?: number;
  recommendScore?: number;
  recommendStartTime?: number | null; // Allow null
  recommendEndTime?: number | null;   // Allow null
  isPaid?: number;
  author?: string;
}) {
  try {
    const result = await db
      .insert(plugins)
      .values({
        ...data,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      })
      .returning();

    return result[0];
  } catch (error) {
    console.error("创建插件失败：", error);
    throw new Error("创建插件失败");
  }
}

// 更新插件
export async function updatePlugin(db: any, id: number, data: Partial<{
  logo: string;
  covers: string;
  name: string;
  categoryId: number;
  url: string;
  description: string;
  isRecommended: number;
  recommendScore: number;
  recommendStartTime: number | null;
  recommendEndTime: number | null;
  isPaid: number;
  author: string;
}>) {
  try {
    const result = await db
      .update(plugins)
      .set({
        ...data,
        updatedAt: Date.now(),
      })
      .where(eq(plugins.id, id))
      .returning();

    return result[0];
  } catch (error) {
    console.error("更新插件失败：", error);
    throw new Error("更新插件失败");
  }
}

// 删除插件
export async function deletePlugin(db: any, id: number) {
  try {
    await db
      .delete(plugins)
      .where(eq(plugins.id, id));

    return true;
  } catch (error) {
    console.error("删除插件失败：", error);
    throw new Error("删除插件失败");
  }
}

// 获取插件统计信息
export async function getPluginsStats(db: any) {
  try {
    const allPlugins = await db.select().from(plugins);
    const totalPlugins = allPlugins.length;
    const recommendedPlugins = allPlugins.filter((plugin: any) => plugin.isRecommended === 1).length;
    const normalPlugins = totalPlugins - recommendedPlugins;

    return {
      totalPlugins,
      recommendedPlugins,
      normalPlugins,
    };
  } catch (error) {
    console.error("获取插件统计失败：", error);
    throw new Error("获取插件统计失败");
  }
} 