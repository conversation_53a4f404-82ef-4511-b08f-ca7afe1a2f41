PRAGMA foreign_keys=OFF;--> statement-breakpoint
CREATE TABLE `__new_categories` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`name` text NOT NULL,
	`description` text,
	`disabled` integer DEFAULT 0 NOT NULL,
	`createdAt` integer DEFAULT 1748512470290 NOT NULL,
	`updatedAt` integer DEFAULT 1748512470290 NOT NULL
);
--> statement-breakpoint
INSERT INTO `__new_categories`("id", "name", "description", "disabled", "createdAt", "updatedAt") SELECT "id", "name", "description", "disabled", "createdAt", "updatedAt" FROM `categories`;--> statement-breakpoint
DROP TABLE `categories`;--> statement-breakpoint
ALTER TABLE `__new_categories` RENAME TO `categories`;--> statement-breakpoint
PRAGMA foreign_keys=ON;--> statement-breakpoint
CREATE UNIQUE INDEX `categories_name_unique` ON `categories` (`name`);--> statement-breakpoint
CREATE TABLE `__new_plugins` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`logo` text NOT NULL,
	`covers` text NOT NULL,
	`name` text NOT NULL,
	`categoryId` integer NOT NULL,
	`url` text NOT NULL,
	`description` text NOT NULL,
	`isRecommended` integer DEFAULT 0 NOT NULL,
	`recommendScore` integer DEFAULT 0 NOT NULL,
	`recommendStartTime` integer,
	`recommendEndTime` integer,
	`createdAt` integer DEFAULT 1748512470290 NOT NULL,
	`updatedAt` integer DEFAULT 1748512470290 NOT NULL,
	`isPaid` integer DEFAULT 0 NOT NULL,
	`author` text DEFAULT '' NOT NULL,
	FOREIGN KEY (`categoryId`) REFERENCES `categories`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
INSERT INTO `__new_plugins`("id", "logo", "covers", "name", "categoryId", "url", "description", "isRecommended", "recommendScore", "recommendStartTime", "recommendEndTime", "createdAt", "updatedAt", "isPaid", "author") SELECT "id", "logo", "covers", "name", "categoryId", "url", "description", "isRecommended", "recommendScore", "recommendStartTime", "recommendEndTime", "createdAt", "updatedAt", "isPaid", "author" FROM `plugins`;--> statement-breakpoint
DROP TABLE `plugins`;--> statement-breakpoint
ALTER TABLE `__new_plugins` RENAME TO `plugins`;--> statement-breakpoint
CREATE TABLE `__new_reserved_members` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`email` text NOT NULL,
	`createdAt` integer DEFAULT 1748512470290 NOT NULL
);
--> statement-breakpoint
INSERT INTO `__new_reserved_members`("id", "email", "createdAt") SELECT "id", "email", "createdAt" FROM `reserved_members`;--> statement-breakpoint
DROP TABLE `reserved_members`;--> statement-breakpoint
ALTER TABLE `__new_reserved_members` RENAME TO `reserved_members`;--> statement-breakpoint
CREATE UNIQUE INDEX `reserved_members_email_unique` ON `reserved_members` (`email`);--> statement-breakpoint
CREATE TABLE `__new_users` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`name` text NOT NULL,
	`email` text NOT NULL,
	`figmaId` text NOT NULL,
	`figmaAccessToken` text NOT NULL,
	`figmaRefreshToken` text NOT NULL,
	`figmaImageUrl` text,
	`tokenExpiresAt` integer,
	`role` text DEFAULT 'user' NOT NULL,
	`isMember` integer DEFAULT 0 NOT NULL,
	`searchCount` integer DEFAULT 0 NOT NULL,
	`createdAt` integer DEFAULT 1748512470290 NOT NULL,
	`updatedAt` integer DEFAULT 1748512470290 NOT NULL
);
--> statement-breakpoint
INSERT INTO `__new_users`("id", "name", "email", "figmaId", "figmaAccessToken", "figmaRefreshToken", "figmaImageUrl", "tokenExpiresAt", "role", "isMember", "searchCount", "createdAt", "updatedAt") SELECT "id", "name", "email", "figmaId", "figmaAccessToken", "figmaRefreshToken", "figmaImageUrl", "tokenExpiresAt", "role", "isMember", "searchCount", "createdAt", "updatedAt" FROM `users`;--> statement-breakpoint
DROP TABLE `users`;--> statement-breakpoint
ALTER TABLE `__new_users` RENAME TO `users`;--> statement-breakpoint
CREATE UNIQUE INDEX `users_email_unique` ON `users` (`email`);--> statement-breakpoint
CREATE UNIQUE INDEX `users_figmaId_unique` ON `users` (`figmaId`);