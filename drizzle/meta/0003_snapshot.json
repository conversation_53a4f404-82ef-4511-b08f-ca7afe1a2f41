{"version": "6", "dialect": "sqlite", "id": "ef7ad97d-7bc3-4b6e-8a77-7ddb3e3baec6", "prevId": "9b86d1fb-204c-4955-b2fc-15969dbd5810", "tables": {"categories": {"name": "categories", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "disabled": {"name": "disabled", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 1748317083283}, "updatedAt": {"name": "updatedAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 1748317083283}}, "indexes": {"categories_name_unique": {"name": "categories_name_unique", "columns": ["name"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "plugins": {"name": "plugins", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "logo": {"name": "logo", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "covers": {"name": "covers", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "categoryId": {"name": "categoryId", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false}, "url": {"name": "url", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "isRecommended": {"name": "isRecommended", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "recommendScore": {"name": "recommendScore", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "recommendStartTime": {"name": "recommendStartTime", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "recommendEndTime": {"name": "recommendEndTime", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 1748317083283}, "updatedAt": {"name": "updatedAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 1748317083283}}, "indexes": {}, "foreignKeys": {"plugins_categoryId_categories_id_fk": {"name": "plugins_categoryId_categories_id_fk", "tableFrom": "plugins", "tableTo": "categories", "columnsFrom": ["categoryId"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "reserved_members": {"name": "reserved_members", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 1748317083283}}, "indexes": {"reserved_members_email_unique": {"name": "reserved_members_email_unique", "columns": ["email"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}, "users": {"name": "users", "columns": {"id": {"name": "id", "type": "integer", "primaryKey": true, "notNull": true, "autoincrement": true}, "name": {"name": "name", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "email": {"name": "email", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "figmaId": {"name": "figmaId", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "figmaAccessToken": {"name": "figmaAccessToken", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "figmaRefreshToken": {"name": "figmaRefreshToken", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false}, "figmaImageUrl": {"name": "figmaImageUrl", "type": "text", "primaryKey": false, "notNull": false, "autoincrement": false}, "tokenExpiresAt": {"name": "tokenExpiresAt", "type": "integer", "primaryKey": false, "notNull": false, "autoincrement": false}, "role": {"name": "role", "type": "text", "primaryKey": false, "notNull": true, "autoincrement": false, "default": "'user'"}, "isMember": {"name": "isMember", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "searchCount": {"name": "searchCount", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 0}, "createdAt": {"name": "createdAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 1748317083283}, "updatedAt": {"name": "updatedAt", "type": "integer", "primaryKey": false, "notNull": true, "autoincrement": false, "default": 1748317083283}}, "indexes": {"users_email_unique": {"name": "users_email_unique", "columns": ["email"], "isUnique": true}, "users_figmaId_unique": {"name": "users_figmaId_unique", "columns": ["figmaId"], "isUnique": true}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "checkConstraints": {}}}, "views": {}, "enums": {}, "_meta": {"schemas": {}, "tables": {}, "columns": {}}, "internal": {"indexes": {}}}