CREATE TABLE `categories` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`name` text NOT NULL,
	`description` text,
	`createdAt` integer DEFAULT 1746608222570 NOT NULL,
	`updatedAt` integer DEFAULT 1746608222570 NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `categories_name_unique` ON `categories` (`name`);--> statement-breakpoint
CREATE TABLE `plugins` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`logo` text NOT NULL,
	`covers` text NOT NULL,
	`name` text NOT NULL,
	`categoryId` integer NOT NULL,
	`url` text NOT NULL,
	`description` text NOT NULL,
	`isRecommended` integer DEFAULT 0 NOT NULL,
	`recommendScore` integer DEFAULT 0 NOT NULL,
	`recommendStartTime` integer,
	`recommendEndTime` integer,
	`createdAt` integer DEFAULT 1746608222570 NOT NULL,
	`updatedAt` integer DEFAULT 1746608222570 NOT NULL,
	FOREIGN KEY (`categoryId`) REFERENCES `categories`(`id`) ON UPDATE no action ON DELETE no action
);
--> statement-breakpoint
CREATE TABLE `users` (
	`id` integer PRIMARY KEY AUTOINCREMENT NOT NULL,
	`name` text NOT NULL,
	`email` text NOT NULL,
	`figmaId` text NOT NULL,
	`figmaAccessToken` text NOT NULL,
	`figmaRefreshToken` text NOT NULL,
	`figmaImageUrl` text,
	`tokenExpiresAt` integer,
	`role` text DEFAULT 'user' NOT NULL,
	`isMember` integer DEFAULT 0 NOT NULL,
	`searchCount` integer DEFAULT 0 NOT NULL,
	`createdAt` integer DEFAULT 1746608222569 NOT NULL,
	`updatedAt` integer DEFAULT 1746608222569 NOT NULL
);
--> statement-breakpoint
CREATE UNIQUE INDEX `users_email_unique` ON `users` (`email`);--> statement-breakpoint
CREATE UNIQUE INDEX `users_figmaId_unique` ON `users` (`figmaId`);--> statement-breakpoint
DROP TABLE `guestBook`;