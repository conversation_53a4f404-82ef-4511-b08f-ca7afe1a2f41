---
description:
globs:
alwaysApply: false
---
# Deployment Guide

This project is deployed on Cloudflare Workers with D1 database.

## Configuration

- [wrangler.jsonc](mdc:wrangler.jsonc) - Main configuration file for Cloudflare Workers
- [.dev.vars](mdc:.dev.vars) - Environment variables for local development

## Deployment Commands

```bash
# Build and deploy to production
pnpm run deploy

# Create a preview deployment
npx wrangler versions upload

# Promote a preview to production
npx wrangler versions deploy
```

## Database Setup

Before deployment, you need to set up a D1 database:

```bash
# Create a new D1 database
npx wrangler d1 create <database-name>

# Update wrangler.jsonc with the database binding
# Then run migrations
pnpm run db:migrate-production
```

## Environment Variables

The application requires these environment variables:

- `FIGMA_CLIENT_ID` - Figma OAuth Client ID
- `FIGMA_CLIENT_SECRET` - Figma OAuth Client Secret 
- `SESSION_SECRET` - Secret for session management

Set these in the Cloudflare dashboard or using Wrangler:

```bash
npx wrangler secret put SESSION_SECRET
```

## Debugging Deployments

To view logs from a deployed application:

```bash
npx wrangler tail
```
