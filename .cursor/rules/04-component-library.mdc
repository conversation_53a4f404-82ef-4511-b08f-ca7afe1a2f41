---
description:
globs:
alwaysApply: false
---
# Component Library Guide

This project uses a custom UI component library built with Radix UI primitives and Tailwind CSS.

## Component Organization

- Reusable UI components are located in [app/components/](mdc:app/components/)
- Page-specific components should be created in the respective route files

## Styling

- Styling is done with Tailwind CSS, configured in [components.json](mdc:components.json)
- The main CSS file is [app/app.css](mdc:app/app.css)

## Key UI Components

The project uses Radix UI primitives for accessible components:

- Buttons, inputs, and form controls
- Dialog, popover, and dropdown components
- Layout components like accordion, tabs, and navigation menus

## Using Components

Components follow a consistent pattern:

```tsx
import { Button } from "~/components/ui/button"

export function MyComponent() {
  return (
    <div className="p-4">
      <Button variant="default">Click Me</Button>
      <Button variant="outline">Cancel</Button>
    </div>
  )
}
```

## Component Variants

Many components use the `class-variance-authority` package for variant styles:

```tsx
// Example button usage with variants
<Button variant="default" size="lg">Large Button</Button>
<Button variant="outline" size="sm">Small Outline Button</Button>
```
