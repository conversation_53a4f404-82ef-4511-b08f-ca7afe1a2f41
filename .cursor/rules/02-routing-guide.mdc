---
description:
globs:
alwaysApply: false
---
# Routing Guide

This project uses React Router 7 with file-system based routes.

## Route Structure

- Routes are defined in the [app/routes/](mdc:app/routes/) directory
- Route files follow these naming conventions:
  - `_index.tsx`: The main/home page
  - `login.tsx`: The login page
  - Files prefixed with `api.` are API routes (e.g., `api.login.tsx`)

## Core Routing Files

- [app/root.tsx](mdc:app/root.tsx) - Root layout component
- [app/routes.ts](mdc:app/routes.ts) - Route exports
- [app/entry.server.tsx](mdc:app/entry.server.tsx) - Server-side entry point

## How to Create New Routes

1. Create a new file in the `app/routes/` directory
2. Export a default component for the page
3. For API routes, use the `api.` prefix

## Route Parameters and Data Loading

Routes can define data loaders and actions using the `loader` and `action` exports:

```tsx
export async function loader({ params }) {
  // Load data for the route
  return { data }
}

export async function action({ request }) {
  // Handle form submissions
  return { result }
}

export default function RouteName() {
  const { data } = useLoaderData()
  // Render component
}
```
