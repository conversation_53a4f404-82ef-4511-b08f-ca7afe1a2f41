---
description: 
globs: 
alwaysApply: false
---
# Project Overview

This is PlugMate, a Figma plugin discovery platform built with React Router and Cloudflare Workers.

## Project Structure

- [app/](mdc:app) - Contains the React application code
  - [app/routes/](mdc:app/routes) - React Router routes
  - [app/components/](mdc:app/components) - Reusable UI components
  - [app/lib/](mdc:app/lib) - Utility functions and helpers
  - [app/hooks/](mdc:app/hooks) - React hooks

- [database/](mdc:database) - Database schema and migrations
  - [database/schema.ts](mdc:database/schema.ts) - Drizzle ORM schema definition

- [workers/](mdc:workers) - Cloudflare Workers
  - [workers/app.ts](mdc:workers/app.ts) - Main worker entry point

## Core Technologies

- **Frontend**: React 19 with React Router 7
- **Styling**: Tailwind CSS 
- **Database**: SQLite with Drizzle ORM
- **Deployment**: Cloudflare Workers
- **Build Tool**: Vite

## Getting Started

1. Install dependencies: `pnpm install`
2. Run database migrations: `pnpm run db:migrate`
3. Start development server: `pnpm run dev`
