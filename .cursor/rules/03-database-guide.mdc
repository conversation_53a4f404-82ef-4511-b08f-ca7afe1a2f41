---
description:
globs:
alwaysApply: false
---
# Database Guide

This project uses SQLite with Drizzle ORM for database operations, deployed as a Cloudflare D1 database.

## Schema

The database schema is defined in [database/schema.ts](mdc:database/schema.ts) and includes:

- `users`: User accounts with Figma authentication
- `categories`: Plugin categories
- `plugins`: Figma plugins listing information

## Database Configuration

- [drizzle.config.ts](mdc:drizzle.config.ts) - Configuration for Drizzle ORM
- Migrations are stored in the [drizzle/](mdc:drizzle/) directory

## Working with the Database

### Running Migrations

```bash
# For local development
pnpm run db:migrate

# For production
pnpm run db:migrate-production
```

### Generating Migrations

```bash
pnpm run db:generate
```

### Querying Data

```typescript
import { eq } from 'drizzle-orm'
import { db } from '~/lib/db'
import { users, plugins } from '~/database/schema'

// Example: Get a user by ID
const user = await db.query.users.findFirst({
  where: eq(users.id, userId)
})

// Example: Get all plugins in a category
const categoryPlugins = await db.query.plugins.findMany({
  where: eq(plugins.categoryId, categoryId)
})
```
