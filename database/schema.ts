import { integer, sqliteTable, text } from "drizzle-orm/sqlite-core";

export const users = sqliteTable("users", {
  id: integer().primaryKey({ autoIncrement: true }),
  name: text().notNull(),
  email: text().notNull().unique(),
  figmaId: text().notNull().unique(),
  figmaAccessToken: text().notNull(),
  figmaRefreshToken: text().notNull(),
  figmaImageUrl: text(),
  tokenExpiresAt: integer(),
  role: text().notNull().default("user"), // "admin" or "user"
  isMember: integer().notNull().default(0), // 0: not a member, 1: is a member
  searchCount: integer().notNull().default(0),
  createdAt: integer().notNull().default(Date.now()),
  updatedAt: integer().notNull().default(Date.now()),
});

// 预定会员表（用于管理员提前添加未注册的会员资格）Reserved Membership
export const reservedMembers = sqliteTable("reserved_members", {
  id: integer().primaryKey({ autoIncrement: true }),
  email: text().notNull().unique(),
  createdAt: integer().notNull().default(Date.now()),
});

export const categories = sqliteTable("categories", {
  id: integer().primaryKey({ autoIncrement: true }),
  name: text().notNull().unique(),
  description: text(),
  disabled: integer().notNull().default(0), // 0: not disabled, 1: disabled
  createdAt: integer().notNull().default(Date.now()),
  updatedAt: integer().notNull().default(Date.now()),
});

export const plugins = sqliteTable("plugins", {
  id: integer().primaryKey({ autoIncrement: true }),
  logo: text().notNull(), // 标识字段 - 图片地址（单图）
  covers: text().notNull(), // 封面字段 - 存储为 JSON 字符串的图片地址数组（可多图）
  name: text().notNull(), // 名称字段
  categoryId: integer().notNull().references(() => categories.id), // 分类字段 - 引用分类表 ID
  url: text().notNull(), // 地址字段 - 在线网址
  description: text().notNull(), // 介绍字段 - 长文本
  isRecommended: integer().notNull().default(0), // 是否被推荐：0-不推荐，1-推荐
  recommendScore: integer().notNull().default(0), // 推荐分值，值越大排序越靠前
  recommendStartTime: integer(), // 推荐开始时间，存储为时间戳
  recommendEndTime: integer(), // 推荐结束时间，存储为时间戳
  createdAt: integer().notNull().default(Date.now()),
  updatedAt: integer().notNull().default(Date.now()),
  isPaid: integer().notNull().default(0), // 是否付费插件：0-免费，1-付费
  author: text().notNull().default(""), // 插件作者
});

export type User = typeof users.$inferSelect
export type Category = typeof categories.$inferSelect
export type Plugin = typeof plugins.$inferSelect
export type ReservedMember = typeof reservedMembers.$inferSelect
