import type { Config } from "drizzle-kit";

export default {
  out: "./drizzle",
  schema: "./database/schema.ts",
  dialect: "sqlite",
  driver: "d1-http",
  dbCredentials: {
    databaseId: "edf85e61-ed5e-41fe-bdcd-1f365123a02c",
    accountId: process.env.CLOUDFLARE_ACCOUNT_ID!,
    token: process.env.CLOUDFLARE_TOKEN!,
  },
  strict: true,
  // 添加这些配置来控制迁移策略
  migrations: {
    table: "__drizzle_migrations",
    schema: "public"
  },
  // 启用更安全的迁移模式
  breakpoints: true,
  // 对于生产环境，考虑添加这个选项
  verbose: true,
} satisfies Config;
