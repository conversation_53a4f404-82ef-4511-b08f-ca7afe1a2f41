# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Tech Stack & Architecture

**Full-stack React application** built with:
- **Frontend**: React 19.1.0 + React Router 7.6.3 (SSR enabled)
- **Backend**: Cloudflare Workers + D1 SQLite database
- **ORM**: Drizzle ORM with sqlite-core
- **Styling**: Tailwind CSS 4.1.11 + shadcn/ui components
- **Auth**: Figma OAuth integration with cookie-based sessions
- **File Storage**: Cloudflare R2 bucket

## Database Schema

**Core tables**:
- `users`: Figma-authenticated users with role-based access (admin/user)
- `plugins`: Plugin listings with metadata, categories, and recommendation system
- `categories`: Plugin categorization with enable/disable flags
- `reserved_members`: Pre-approved email list for member access

## Key Commands

```bash
# Development
npm run dev              # Start dev server with HMR
npm run db:migrate       # Run local database migrations (required for first setup)
npm run typecheck        # Type checking with <PERSON>flare types
npm run preview          # Build and preview production locally
npm run cf-typegen       # Generate Cloudflare Worker types

# Production
npm run build           # Build for production
npm run deploy          # Build and deploy to Cloudflare
npm run db:migrate-production  # Run production migrations

# Database
npm run db:generate     # Generate new migration files

# Deployment (additional Wrangler commands)
npx wrangler versions upload    # Deploy preview URL
npx wrangler versions deploy    # Promote version to production
```

## Project Structure

- `/app` - React Router application code
  - `/components` - UI components (shadcn/ui based)
  - `/lib` - Server-side data access functions
  - `/routes` - File-based routing with admin panel
- `/database` - Drizzle ORM schema definitions
- `/workers` - Cloudflare Worker entry point
- `/drizzle` - Database migrations

## Authentication Flow

1. Figma OAuth via `/login` route
2. Session stored in encrypted cookies
3. Role-based access: `admin` (full access) vs `user` (member-only)
4. Member verification via `reserved_members` table or `isMember` flag

## Cloudflare Integration

- **D1 Database**: SQLite with binding `DB`
- **R2 Storage**: File storage with binding `R2_BUCKET`
- **Environment Variables**: Configured in `wrangler.jsonc`
- **Custom Domain**: `plugmate.figmod.top`

## Admin Features

Admin panel routes under `/admin`:
- User management (promote/demote members)
- Plugin management (CRUD operations)
- Category management
- Plugin recommendation system with scheduling

## Setup Requirements

For initial setup:
1. Run `npm install` to install dependencies
2. Run `npm run db:migrate` to initialize the local database
3. Configure Figma OAuth credentials in Cloudflare environment variables
4. For production: Create D1 database with `npx wrangler d1 create <name>` and update wrangler.jsonc