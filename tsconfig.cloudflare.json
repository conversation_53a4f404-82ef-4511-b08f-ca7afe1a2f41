{"extends": "./tsconfig.json", "include": [".react-router/types/**/*", "app/**/*", "app/**/.server/**/*", "app/**/.client/**/*", "database/**/*", "workers/**/*", "worker-configuration.d.ts"], "compilerOptions": {"composite": true, "strict": true, "lib": ["DOM", "DOM.Iterable", "ES2022"], "types": ["@cloudflare/workers-types", "node", "vite/client"], "target": "ES2022", "module": "ES2022", "moduleResolution": "bundler", "jsx": "react-jsx", "baseUrl": ".", "rootDirs": [".", "./.react-router/types"], "paths": {"database/*": ["./database/*"], "~/*": ["./app/*"]}, "esModuleInterop": true, "resolveJsonModule": true}}