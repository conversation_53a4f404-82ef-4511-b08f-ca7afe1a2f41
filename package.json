{"name": "plugmate", "private": true, "type": "module", "scripts": {"build": "react-router build", "cf-typegen": "wrangler types", "db:generate": "dotenv -- drizzle-kit generate", "db:migrate": "wrangler d1 migrations apply --local DB", "db:migrate-production": "dotenv -- drizzle-kit migrate", "deploy": "npm run build && wrangler deploy", "dev": "react-router dev", "preview": "npm run build && vite preview", "typecheck": "npm run cf-typegen && react-router typegen && tsc -b"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@mjackson/form-data-parser": "^0.7.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-aspect-ratio": "^1.1.7", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@radix-ui/react-toggle-group": "^1.1.10", "@radix-ui/react-tooltip": "^1.2.7", "@react-router/fs-routes": "^7.7.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "drizzle-orm": "~0.36.4", "embla-carousel-react": "^8.6.0", "input-otp": "^1.4.2", "isbot": "^5.1.29", "jose": "^6.0.12", "ky": "^1.8.2", "lucide-react": "^0.507.0", "motion": "^12.23.12", "next-themes": "^0.4.6", "react": "^19.1.1", "react-day-picker": "9.7.0", "react-dom": "^19.1.1", "react-hook-form": "^7.62.0", "react-resizable-panels": "^3.0.4", "react-router": "^7.7.1", "recharts": "^2.15.4", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "vaul": "^1.1.2", "zod": "^3.25.76"}, "devDependencies": {"@cloudflare/vite-plugin": "^1.11.0", "@cloudflare/workers-types": "^4.20250802.0", "@react-router/dev": "^7.7.1", "@tailwindcss/vite": "^4.1.11", "@types/node": "^20.19.9", "@types/react": "^19.1.9", "@types/react-dom": "^19.1.7", "dotenv-cli": "^7.4.4", "drizzle-kit": "~0.31.4", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.6", "typescript": "^5.9.2", "vite": "^6.3.5", "vite-tsconfig-paths": "^5.1.4", "wrangler": "^4.27.0"}, "packageManager": "pnpm@10.11.1+sha512.e519b9f7639869dc8d5c3c5dfef73b3f091094b0a006d7317353c72b124e80e1afd429732e28705ad6bfa1ee879c1fce46c128ccebd3192101f43dd67c667912"}